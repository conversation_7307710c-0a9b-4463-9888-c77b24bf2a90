import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../../core/utils/logger.dart';
import '../../../domain/entities/video_info.dart';
import '../../../domain/usecases/extract_video_info.dart';

part 'video_extraction_event.dart';
part 'video_extraction_state.dart';

/// BLoC for video extraction functionality
@injectable
class VideoExtractionBloc extends Bloc<VideoExtractionEvent, VideoExtractionState> {
  VideoExtractionBloc(
    this._extractVideoInfo,
    this._logger,
  ) : super(const VideoExtractionInitial()) {
    on<VideoExtractionStarted>(_onVideoExtractionStarted);
    on<VideoExtractionCleared>(_onVideoExtractionCleared);
  }

  final ExtractVideoInfo _extractVideoInfo;
  final AppLogger _logger;

  Future<void> _onVideoExtractionStarted(
    VideoExtractionStarted event,
    Emitter<VideoExtractionState> emit,
  ) async {
    emit(const VideoExtractionLoading());

    _logger.info('Starting video extraction for URL: ${event.url}');

    final result = await _extractVideoInfo(
      ExtractVideoInfoParams(
        url: event.url,
        useCache: event.useCache,
        cacheResult: event.cacheResult,
      ),
    );

    result.fold(
      (failure) {
        _logger.error('Video extraction failed: ${failure.message}');
        emit(VideoExtractionFailure(
          message: failure.message,
          code: failure.code,
        ));
      },
      (videoInfo) {
        _logger.info('Video extraction successful: ${videoInfo.title}');
        emit(VideoExtractionSuccess(videoInfo: videoInfo));
      },
    );
  }

  void _onVideoExtractionCleared(
    VideoExtractionCleared event,
    Emitter<VideoExtractionState> emit,
  ) {
    _logger.debug('Video extraction state cleared');
    emit(const VideoExtractionInitial());
  }
}
