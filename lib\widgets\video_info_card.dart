import 'package:flutter/material.dart';
import '../models/video_info.dart';
import '../models/video_quality.dart';
import '../providers/theme_provider.dart';
import 'quality_selector.dart';

class VideoInfoCard extends StatefulWidget {
  final VideoInfo videoInfo;
  final Function(VideoQuality) onDownload;

  const VideoInfoCard({
    super.key,
    required this.videoInfo,
    required this.onDownload,
  });

  @override
  State<VideoInfoCard> createState() => _VideoInfoCardState();
}

class _VideoInfoCardState extends State<VideoInfoCard> {
  VideoQuality? _selectedQuality;

  @override
  void initState() {
    super.initState();
    if (widget.videoInfo.availableQualities.isNotEmpty) {
      _selectedQuality = widget.videoInfo.availableQualities.first;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Thumbnail and basic info
          _buildHeader(),

          // Video details
          _buildDetails(),

          // Quality selector
          if (widget.videoInfo.availableQualities.isNotEmpty) ...[
            const Divider(),
            _buildQualitySelector(),
          ],

          // Download button
          const Divider(),
          _buildDownloadButton(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
        image:
            widget.videoInfo.thumbnailUrl.isNotEmpty
                ? DecorationImage(
                  image: NetworkImage(widget.videoInfo.thumbnailUrl),
                  fit: BoxFit.cover,
                  onError: (error, stackTrace) {
                    // Handle image loading error
                  },
                )
                : null,
        gradient:
            widget.videoInfo.thumbnailUrl.isEmpty
                ? ThemeProvider.primaryGradient
                : null,
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.transparent, Colors.black.withOpacity(0.7)],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Platform badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: ThemeProvider.primaryBlue,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.videoInfo.platform,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // Title
              Text(
                widget.videoInfo.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 3,
                      color: Colors.black54,
                    ),
                  ],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetails() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Duration and uploader
          Row(
            children: [
              if (widget.videoInfo.duration.inSeconds > 0) ...[
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  _formatDuration(widget.videoInfo.duration),
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
              ],

              if (widget.videoInfo.uploader != null) ...[
                if (widget.videoInfo.duration.inSeconds > 0) ...[
                  const SizedBox(width: 16),
                  Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[400],
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                Icon(Icons.person, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    widget.videoInfo.uploader!,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),

          // Upload date
          if (widget.videoInfo.uploadDate != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  _formatDate(widget.videoInfo.uploadDate!),
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
              ],
            ),
          ],

          // Description
          if (widget.videoInfo.description != null &&
              widget.videoInfo.description!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              'Description',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              widget.videoInfo.description!,
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQualitySelector() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select Quality & Format',
            style: Theme.of(
              context,
            ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          QualitySelector(
            qualities: widget.videoInfo.availableQualities,
            selectedQuality: _selectedQuality,
            onQualitySelected: (quality) {
              setState(() {
                _selectedQuality = quality;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadButton() {
    final canDownload = _selectedQuality != null;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed:
              canDownload ? () => widget.onDownload(_selectedQuality!) : null,
          icon: const Icon(Icons.download),
          label: Text(
            canDownload
                ? 'Download ${_selectedQuality!.displayName}'
                : 'No quality available',
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: ThemeProvider.accentGreen,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks > 1 ? 's' : ''} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years year${years > 1 ? 's' : ''} ago';
    }
  }
}
