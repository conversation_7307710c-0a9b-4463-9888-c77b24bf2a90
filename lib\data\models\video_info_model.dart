import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

import '../../domain/entities/video_info.dart';
import 'video_quality_model.dart';

part 'video_info_model.freezed.dart';
part 'video_info_model.g.dart';

@freezed
@HiveType(typeId: 0)
class VideoInfoModel with _$VideoInfoModel {
  const factory VideoInfoModel({
    @HiveField(0) required String id,
    @HiveField(1) required String title,
    @HiveField(2) required String url,
    @HiveField(3) required String thumbnailUrl,
    @HiveField(4) required String platform,
    @HiveField(5) required int durationInSeconds,
    @HiveField(6) required List<VideoQualityModel> availableQualities,
    @HiveField(7) String? description,
    @HiveField(8) String? uploader,
    @HiveField(9) DateTime? uploadDate,
    @HiveField(10) int? viewCount,
    @HiveField(11) int? likeCount,
    @HiveField(12) List<String>? tags,
    @HiveField(13) bool? hasWatermark,
    @HiveField(14) DateTime? extractedAt,
  }) = _VideoInfoModel;

  factory VideoInfoModel.fromJson(Map<String, dynamic> json) =>
      _$VideoInfoModelFromJson(json);
}

/// Extension to convert model to entity
extension VideoInfoModelX on VideoInfoModel {
  VideoInfo toEntity() {
    return VideoInfo(
      id: id,
      title: title,
      url: url,
      thumbnailUrl: thumbnailUrl,
      platform: platform,
      duration: Duration(seconds: durationInSeconds),
      availableQualities: availableQualities.map((q) => q.toEntity()).toList(),
      description: description,
      uploader: uploader,
      uploadDate: uploadDate,
      viewCount: viewCount,
      likeCount: likeCount,
      tags: tags,
      hasWatermark: hasWatermark ?? false,
      extractedAt: extractedAt ?? DateTime.now(),
    );
  }
}

/// Extension to convert entity to model
extension VideoInfoX on VideoInfo {
  VideoInfoModel toModel() {
    return VideoInfoModel(
      id: id,
      title: title,
      url: url,
      thumbnailUrl: thumbnailUrl,
      platform: platform,
      durationInSeconds: duration.inSeconds,
      availableQualities: availableQualities.map((q) => q.toModel()).toList(),
      description: description,
      uploader: uploader,
      uploadDate: uploadDate,
      viewCount: viewCount,
      likeCount: likeCount,
      tags: tags,
      hasWatermark: hasWatermark,
      extractedAt: extractedAt,
    );
  }
}
