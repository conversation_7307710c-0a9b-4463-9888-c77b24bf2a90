import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../providers/download_provider.dart';
import '../models/download_item.dart';
import '../widgets/download_item_card.dart';
import '../widgets/enhanced_download_card.dart';
import '../widgets/download_stats_card.dart';

class DownloadsScreen extends StatefulWidget {
  const DownloadsScreen({super.key});

  @override
  State<DownloadsScreen> createState() => _DownloadsScreenState();
}

class _DownloadsScreenState extends State<DownloadsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  String _sortBy = 'date';
  bool _sortAscending = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);

    // Refresh downloads when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DownloadProvider>(context, listen: false).refreshDownloads();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    final downloadProvider = Provider.of<DownloadProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(appProvider.getString('downloads')),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(
              text: 'All (${downloadProvider.downloads.length})',
              icon: const Icon(Icons.list),
            ),
            Tab(
              text: 'Active (${downloadProvider.activeDownloads.length})',
              icon: const Icon(Icons.download),
            ),
            Tab(
              text: 'Completed (${downloadProvider.completedDownloads.length})',
              icon: const Icon(Icons.check_circle),
            ),
            Tab(
              text: 'Failed (${downloadProvider.failedDownloads.length})',
              icon: const Icon(Icons.error),
            ),
            Tab(
              text: 'Paused (${downloadProvider.pausedDownloads.length})',
              icon: const Icon(Icons.pause_circle),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'sort',
                    child: ListTile(
                      leading: Icon(Icons.sort),
                      title: Text('Sort'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'refresh',
                    child: ListTile(
                      leading: Icon(Icons.refresh),
                      title: Text('Refresh'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'pause_all',
                    child: ListTile(
                      leading: Icon(Icons.pause),
                      title: Text('Pause All'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'resume_all',
                    child: ListTile(
                      leading: Icon(Icons.play_arrow),
                      title: Text('Resume All'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'retry_failed',
                    child: ListTile(
                      leading: Icon(Icons.refresh),
                      title: Text('Retry Failed'),
                      dense: true,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'clear_completed',
                    child: ListTile(
                      leading: Icon(Icons.clear),
                      title: Text('Clear Completed'),
                      dense: true,
                    ),
                  ),
                ],
          ),
        ],
      ),

      body: Column(
        children: [
          // Stats Card
          const DownloadStatsCard(),

          // Downloads List
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildDownloadsList(downloadProvider.downloads),
                _buildDownloadsList(downloadProvider.activeDownloads),
                _buildDownloadsList(downloadProvider.completedDownloads),
                _buildDownloadsList(downloadProvider.failedDownloads),
                _buildDownloadsList(downloadProvider.pausedDownloads),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDownloadsList(List<DownloadItem> downloads) {
    if (downloads.isEmpty) {
      return _buildEmptyState();
    }

    // Apply search filter
    List<DownloadItem> filteredDownloads = downloads;
    if (_searchQuery.isNotEmpty) {
      final downloadProvider = Provider.of<DownloadProvider>(
        context,
        listen: false,
      );
      filteredDownloads =
          downloadProvider
              .searchDownloads(_searchQuery)
              .where((item) => downloads.contains(item))
              .toList();
    }

    // Apply sorting
    final downloadProvider = Provider.of<DownloadProvider>(
      context,
      listen: false,
    );
    filteredDownloads =
        downloadProvider
            .sortDownloads(_sortBy, ascending: _sortAscending)
            .where((item) => downloads.contains(item))
            .toList();

    if (filteredDownloads.isEmpty) {
      return _buildNoResultsState();
    }

    return RefreshIndicator(
      onRefresh: () async {
        await Provider.of<DownloadProvider>(
          context,
          listen: false,
        ).refreshDownloads();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: filteredDownloads.length,
        itemBuilder: (context, index) {
          final download = filteredDownloads[index];
          return EnhancedDownloadCard(
            item: download,
            onRetry:
                download.status == DownloadStatus.failed
                    ? () => _handleDownloadAction('retry', download)
                    : null,
            onPause:
                download.status == DownloadStatus.downloading
                    ? () => _handleDownloadAction('pause', download)
                    : null,
            onResume:
                download.status == DownloadStatus.paused
                    ? () => _handleDownloadAction('resume', download)
                    : null,
            onCancel:
                (download.status == DownloadStatus.downloading ||
                        download.status == DownloadStatus.paused)
                    ? () => _handleDownloadAction('cancel', download)
                    : null,
            onDelete: () => _handleDownloadAction('delete', download),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    final appProvider = Provider.of<AppProvider>(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.download_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            appProvider.getString('no_downloads'),
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            appProvider.getString('no_downloads_desc'),
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // Switch to home tab
              DefaultTabController.of(context).animateTo(0);
            },
            icon: const Icon(Icons.add),
            label: const Text('Start Downloading'),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No results found',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or filters',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
            },
            child: const Text('Clear Search'),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        String query = _searchQuery;
        return AlertDialog(
          title: const Text('Search Downloads'),
          content: TextField(
            autofocus: true,
            decoration: const InputDecoration(
              hintText: 'Enter search term...',
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: (value) => query = value,
            controller: TextEditingController(text: _searchQuery),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _searchQuery = query;
                });
                Navigator.of(context).pop();
              },
              child: const Text('Search'),
            ),
          ],
        );
      },
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sort Downloads'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile<String>(
                  title: const Text('Date'),
                  value: 'date',
                  groupValue: _sortBy,
                  onChanged: (value) => setState(() => _sortBy = value!),
                ),
                RadioListTile<String>(
                  title: const Text('Title'),
                  value: 'title',
                  groupValue: _sortBy,
                  onChanged: (value) => setState(() => _sortBy = value!),
                ),
                RadioListTile<String>(
                  title: const Text('Size'),
                  value: 'size',
                  groupValue: _sortBy,
                  onChanged: (value) => setState(() => _sortBy = value!),
                ),
                RadioListTile<String>(
                  title: const Text('Platform'),
                  value: 'platform',
                  groupValue: _sortBy,
                  onChanged: (value) => setState(() => _sortBy = value!),
                ),
                RadioListTile<String>(
                  title: const Text('Status'),
                  value: 'status',
                  groupValue: _sortBy,
                  onChanged: (value) => setState(() => _sortBy = value!),
                ),
                const Divider(),
                SwitchListTile(
                  title: const Text('Ascending'),
                  value: _sortAscending,
                  onChanged: (value) => setState(() => _sortAscending = value),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {});
                },
                child: const Text('Apply'),
              ),
            ],
          ),
    );
  }

  void _handleMenuAction(String action) async {
    final downloadProvider = Provider.of<DownloadProvider>(
      context,
      listen: false,
    );

    switch (action) {
      case 'sort':
        _showSortDialog();
        break;
      case 'refresh':
        await downloadProvider.refreshDownloads();
        break;
      case 'pause_all':
        await downloadProvider.pauseAllDownloads();
        _showSnackBar('All downloads paused');
        break;
      case 'resume_all':
        await downloadProvider.resumeAllDownloads();
        _showSnackBar('All downloads resumed');
        break;
      case 'retry_failed':
        await downloadProvider.retryAllFailedDownloads();
        _showSnackBar('Retrying failed downloads');
        break;
      case 'clear_completed':
        await _showClearCompletedDialog();
        break;
    }
  }

  Future<void> _showClearCompletedDialog() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Clear Completed Downloads'),
            content: const Text(
              'This will remove all completed downloads from the list. '
              'The downloaded files will remain on your device.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Clear'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final downloadProvider = Provider.of<DownloadProvider>(
        context,
        listen: false,
      );
      await downloadProvider.clearCompletedDownloads();
      _showSnackBar('Completed downloads cleared');
    }
  }

  void _handleDownloadAction(String action, DownloadItem download) async {
    final downloadProvider = Provider.of<DownloadProvider>(
      context,
      listen: false,
    );

    switch (action) {
      case 'pause':
        await downloadProvider.pauseDownload(download.id);
        _showSnackBar('Download paused');
        break;
      case 'resume':
        await downloadProvider.resumeDownload(download.id);
        _showSnackBar('Download resumed');
        break;
      case 'cancel':
        await downloadProvider.cancelDownload(download.id);
        _showSnackBar('Download cancelled');
        break;
      case 'retry':
        await downloadProvider.retryDownload(download.id);
        _showSnackBar('Download restarted');
        break;
      case 'delete':
        await _showDeleteDialog(download);
        break;
      case 'play':
        // TODO: Implement video player
        _showSnackBar('Video player not implemented yet');
        break;
      case 'share':
        // TODO: Implement sharing
        _showSnackBar('Sharing not implemented yet');
        break;
      case 'info':
        _showInfoDialog(download);
        break;
    }
  }

  Future<void> _showDeleteDialog(DownloadItem download) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Download'),
            content: Text(
              'Are you sure you want to delete "${download.title}"?\n\n'
              'This will remove the download from the list and delete the file from your device.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final downloadProvider = Provider.of<DownloadProvider>(
        context,
        listen: false,
      );
      await downloadProvider.deleteDownload(download.id);
      _showSnackBar('Download deleted');
    }
  }

  void _showInfoDialog(DownloadItem download) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(download.title),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _InfoRow('Platform', download.platform),
                  _InfoRow('Quality', download.quality),
                  _InfoRow('Format', download.format),
                  _InfoRow('File Size', download.formattedFileSize),
                  _InfoRow('Progress', download.formattedProgress),
                  _InfoRow('Status', download.status.name),
                  _InfoRow('Created', _formatDate(download.createdAt)),
                  if (download.completedAt != null)
                    _InfoRow('Completed', _formatDate(download.completedAt!)),
                  if (download.errorMessage != null)
                    _InfoRow('Error', download.errorMessage!),
                  _InfoRow('File Path', download.filePath),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), behavior: SnackBarBehavior.floating),
    );
  }
}

class _InfoRow extends StatelessWidget {
  final String label;
  final String value;

  const _InfoRow(this.label, this.value);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value, style: const TextStyle(fontSize: 14))),
        ],
      ),
    );
  }
}
