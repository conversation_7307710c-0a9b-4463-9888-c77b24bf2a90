enum DownloadStatus {
  pending,
  downloading,
  paused,
  completed,
  failed,
  cancelled,
}

class DownloadItem {
  final String id;
  final String videoId;
  final String title;
  final String url;
  final String thumbnailUrl;
  final String platform;
  final String quality;
  final String format;
  final String filePath;
  final int? totalBytes;
  final int downloadedBytes;
  final DownloadStatus status;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? errorMessage;
  final double progress;

  DownloadItem({
    required this.id,
    required this.videoId,
    required this.title,
    required this.url,
    required this.thumbnailUrl,
    required this.platform,
    required this.quality,
    required this.format,
    required this.filePath,
    this.totalBytes,
    this.downloadedBytes = 0,
    this.status = DownloadStatus.pending,
    required this.createdAt,
    this.completedAt,
    this.errorMessage,
    this.progress = 0.0,
  });

  factory DownloadItem.fromJson(Map<String, dynamic> json) {
    return DownloadItem(
      id: json['id'],
      videoId: json['video_id'],
      title: json['title'],
      url: json['url'],
      thumbnailUrl: json['thumbnail_url'],
      platform: json['platform'],
      quality: json['quality'],
      format: json['format'],
      filePath: json['file_path'],
      totalBytes: json['total_bytes'],
      downloadedBytes: json['downloaded_bytes'] ?? 0,
      status: DownloadStatus.values[json['status'] ?? 0],
      createdAt: DateTime.parse(json['created_at']),
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at']) 
          : null,
      errorMessage: json['error_message'],
      progress: json['progress']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'video_id': videoId,
      'title': title,
      'url': url,
      'thumbnail_url': thumbnailUrl,
      'platform': platform,
      'quality': quality,
      'format': format,
      'file_path': filePath,
      'total_bytes': totalBytes,
      'downloaded_bytes': downloadedBytes,
      'status': status.index,
      'created_at': createdAt.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
      'error_message': errorMessage,
      'progress': progress,
    };
  }

  DownloadItem copyWith({
    String? id,
    String? videoId,
    String? title,
    String? url,
    String? thumbnailUrl,
    String? platform,
    String? quality,
    String? format,
    String? filePath,
    int? totalBytes,
    int? downloadedBytes,
    DownloadStatus? status,
    DateTime? createdAt,
    DateTime? completedAt,
    String? errorMessage,
    double? progress,
  }) {
    return DownloadItem(
      id: id ?? this.id,
      videoId: videoId ?? this.videoId,
      title: title ?? this.title,
      url: url ?? this.url,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      platform: platform ?? this.platform,
      quality: quality ?? this.quality,
      format: format ?? this.format,
      filePath: filePath ?? this.filePath,
      totalBytes: totalBytes ?? this.totalBytes,
      downloadedBytes: downloadedBytes ?? this.downloadedBytes,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      errorMessage: errorMessage ?? this.errorMessage,
      progress: progress ?? this.progress,
    );
  }

  String get formattedFileSize {
    if (totalBytes == null) return 'Unknown';
    
    final bytes = totalBytes!;
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  String get formattedProgress {
    return '${(progress * 100).toStringAsFixed(1)}%';
  }

  bool get isCompleted => status == DownloadStatus.completed;
  bool get isDownloading => status == DownloadStatus.downloading;
  bool get isPaused => status == DownloadStatus.paused;
  bool get isFailed => status == DownloadStatus.failed;
  bool get isPending => status == DownloadStatus.pending;
}
