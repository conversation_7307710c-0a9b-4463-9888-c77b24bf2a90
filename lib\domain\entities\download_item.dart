import 'package:equatable/equatable.dart';

/// Download status enumeration
enum DownloadStatus {
  pending,
  downloading,
  paused,
  completed,
  failed,
  cancelled,
}

/// Download item entity
class DownloadItem extends Equatable {
  const DownloadItem({
    required this.id,
    required this.videoId,
    required this.title,
    required this.url,
    required this.downloadUrl,
    required this.thumbnailUrl,
    required this.platform,
    required this.quality,
    required this.format,
    required this.filePath,
    required this.status,
    required this.progress,
    required this.createdAt,
    this.startedAt,
    this.completedAt,
    this.totalBytes,
    this.downloadedBytes,
    this.errorMessage,
    this.retryCount = 0,
    this.downloadSpeed,
    this.estimatedTimeRemaining,
  });

  final String id;
  final String videoId;
  final String title;
  final String url;
  final String downloadUrl;
  final String thumbnailUrl;
  final String platform;
  final String quality;
  final String format;
  final String filePath;
  final DownloadStatus status;
  final double progress;
  final DateTime createdAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final int? totalBytes;
  final int? downloadedBytes;
  final String? errorMessage;
  final int retryCount;
  final double? downloadSpeed;
  final Duration? estimatedTimeRemaining;

  /// Get formatted progress percentage
  String get formattedProgress {
    return '${(progress * 100).toStringAsFixed(1)}%';
  }

  /// Get formatted file size
  String get formattedFileSize {
    if (totalBytes == null) return 'Unknown size';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    double size = totalBytes!.toDouble();
    int unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  /// Get formatted download speed
  String get formattedDownloadSpeed {
    if (downloadSpeed == null) return 'N/A';
    
    const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
    double speed = downloadSpeed!;
    int unitIndex = 0;
    
    while (speed >= 1024 && unitIndex < units.length - 1) {
      speed /= 1024;
      unitIndex++;
    }
    
    return '${speed.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  /// Get formatted estimated time remaining
  String get formattedTimeRemaining {
    if (estimatedTimeRemaining == null) return 'N/A';
    
    final duration = estimatedTimeRemaining!;
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Get download duration
  Duration? get downloadDuration {
    if (startedAt == null) return null;
    final endTime = completedAt ?? DateTime.now();
    return endTime.difference(startedAt!);
  }

  /// Get formatted download duration
  String get formattedDownloadDuration {
    final duration = downloadDuration;
    if (duration == null) return 'N/A';
    
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Check if download is active
  bool get isActive => status == DownloadStatus.downloading;

  /// Check if download is completed
  bool get isCompleted => status == DownloadStatus.completed;

  /// Check if download has failed
  bool get hasFailed => status == DownloadStatus.failed;

  /// Check if download is paused
  bool get isPaused => status == DownloadStatus.paused;

  /// Check if download can be resumed
  bool get canResume => status == DownloadStatus.paused || status == DownloadStatus.failed;

  /// Check if download can be cancelled
  bool get canCancel => status == DownloadStatus.downloading || status == DownloadStatus.paused;

  /// Check if download can be retried
  bool get canRetry => status == DownloadStatus.failed;

  /// Get status display text
  String get statusText {
    switch (status) {
      case DownloadStatus.pending:
        return 'Pending';
      case DownloadStatus.downloading:
        return 'Downloading';
      case DownloadStatus.paused:
        return 'Paused';
      case DownloadStatus.completed:
        return 'Completed';
      case DownloadStatus.failed:
        return 'Failed';
      case DownloadStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Create a copy with updated values
  DownloadItem copyWith({
    String? id,
    String? videoId,
    String? title,
    String? url,
    String? downloadUrl,
    String? thumbnailUrl,
    String? platform,
    String? quality,
    String? format,
    String? filePath,
    DownloadStatus? status,
    double? progress,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? completedAt,
    int? totalBytes,
    int? downloadedBytes,
    String? errorMessage,
    int? retryCount,
    double? downloadSpeed,
    Duration? estimatedTimeRemaining,
  }) {
    return DownloadItem(
      id: id ?? this.id,
      videoId: videoId ?? this.videoId,
      title: title ?? this.title,
      url: url ?? this.url,
      downloadUrl: downloadUrl ?? this.downloadUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      platform: platform ?? this.platform,
      quality: quality ?? this.quality,
      format: format ?? this.format,
      filePath: filePath ?? this.filePath,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      totalBytes: totalBytes ?? this.totalBytes,
      downloadedBytes: downloadedBytes ?? this.downloadedBytes,
      errorMessage: errorMessage ?? this.errorMessage,
      retryCount: retryCount ?? this.retryCount,
      downloadSpeed: downloadSpeed ?? this.downloadSpeed,
      estimatedTimeRemaining: estimatedTimeRemaining ?? this.estimatedTimeRemaining,
    );
  }

  @override
  List<Object?> get props => [
        id,
        videoId,
        title,
        url,
        downloadUrl,
        thumbnailUrl,
        platform,
        quality,
        format,
        filePath,
        status,
        progress,
        createdAt,
        startedAt,
        completedAt,
        totalBytes,
        downloadedBytes,
        errorMessage,
        retryCount,
        downloadSpeed,
        estimatedTimeRemaining,
      ];

  @override
  String toString() => 'DownloadItem(id: $id, title: $title, status: $status, progress: $progress)';
}
