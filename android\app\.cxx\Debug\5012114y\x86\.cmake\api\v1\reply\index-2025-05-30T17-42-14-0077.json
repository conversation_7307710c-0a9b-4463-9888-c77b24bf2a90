{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-09143044a4267365386d.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-6a6032d6c14909ee8351.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-5d5dcafefd62395acb95.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-6a6032d6c14909ee8351.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-5d5dcafefd62395acb95.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-09143044a4267365386d.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}