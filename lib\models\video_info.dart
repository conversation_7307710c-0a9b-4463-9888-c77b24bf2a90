import 'video_quality.dart';

class VideoInfo {
  final String id;
  final String title;
  final String url;
  final String thumbnailUrl;
  final String platform;
  final Duration duration;
  final List<VideoQuality> availableQualities;
  final String? description;
  final String? uploader;
  final DateTime? uploadDate;

  VideoInfo({
    required this.id,
    required this.title,
    required this.url,
    required this.thumbnailUrl,
    required this.platform,
    required this.duration,
    required this.availableQualities,
    this.description,
    this.uploader,
    this.uploadDate,
  });

  factory VideoInfo.fromJson(Map<String, dynamic> json) {
    return VideoInfo(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      url: json['url'] ?? '',
      thumbnailUrl: json['thumbnail_url'] ?? '',
      platform: json['platform'] ?? '',
      duration: Duration(seconds: json['duration'] ?? 0),
      availableQualities:
          (json['qualities'] as List<dynamic>?)
              ?.map((q) => VideoQuality.fromJson(q))
              .toList() ??
          [],
      description: json['description'],
      uploader: json['uploader'],
      uploadDate:
          json['upload_date'] != null
              ? DateTime.tryParse(json['upload_date'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'url': url,
      'thumbnail_url': thumbnailUrl,
      'platform': platform,
      'duration': duration.inSeconds,
      'qualities': availableQualities.map((q) => q.toJson()).toList(),
      'description': description,
      'uploader': uploader,
      'upload_date': uploadDate?.toIso8601String(),
    };
  }
}
