class VideoInfo {
  final String id;
  final String title;
  final String url;
  final String thumbnailUrl;
  final String platform;
  final Duration duration;
  final List<VideoQuality> availableQualities;
  final String? description;
  final String? uploader;
  final DateTime? uploadDate;

  VideoInfo({
    required this.id,
    required this.title,
    required this.url,
    required this.thumbnailUrl,
    required this.platform,
    required this.duration,
    required this.availableQualities,
    this.description,
    this.uploader,
    this.uploadDate,
  });

  factory VideoInfo.fromJson(Map<String, dynamic> json) {
    return VideoInfo(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      url: json['url'] ?? '',
      thumbnailUrl: json['thumbnail_url'] ?? '',
      platform: json['platform'] ?? '',
      duration: Duration(seconds: json['duration'] ?? 0),
      availableQualities: (json['qualities'] as List<dynamic>?)
          ?.map((q) => VideoQuality.fromJson(q))
          .toList() ?? [],
      description: json['description'],
      uploader: json['uploader'],
      uploadDate: json['upload_date'] != null 
          ? DateTime.tryParse(json['upload_date']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'url': url,
      'thumbnail_url': thumbnailUrl,
      'platform': platform,
      'duration': duration.inSeconds,
      'qualities': availableQualities.map((q) => q.toJson()).toList(),
      'description': description,
      'uploader': uploader,
      'upload_date': uploadDate?.toIso8601String(),
    };
  }
}

class VideoQuality {
  final String quality;
  final String format;
  final String downloadUrl;
  final int? fileSize;
  final bool hasAudio;
  final bool hasVideo;

  VideoQuality({
    required this.quality,
    required this.format,
    required this.downloadUrl,
    this.fileSize,
    required this.hasAudio,
    required this.hasVideo,
  });

  factory VideoQuality.fromJson(Map<String, dynamic> json) {
    return VideoQuality(
      quality: json['quality'] ?? '',
      format: json['format'] ?? '',
      downloadUrl: json['download_url'] ?? '',
      fileSize: json['file_size'],
      hasAudio: json['has_audio'] ?? false,
      hasVideo: json['has_video'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'quality': quality,
      'format': format,
      'download_url': downloadUrl,
      'file_size': fileSize,
      'has_audio': hasAudio,
      'has_video': hasVideo,
    };
  }

  String get displayName {
    if (hasVideo && hasAudio) {
      return '$quality (Video + Audio)';
    } else if (hasVideo) {
      return '$quality (Video Only)';
    } else if (hasAudio) {
      return 'Audio Only ($quality)';
    }
    return quality;
  }
}
