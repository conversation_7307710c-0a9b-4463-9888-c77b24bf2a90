import 'package:flutter/foundation.dart';

/// Application configuration constants
class AppConfig {
  AppConfig._();

  // App Information
  static const String appName = 'Video Downloader Pro';
  static const String appVersion = '1.0.0';
  static const int appBuildNumber = 1;

  // Environment
  static const String environment = kDebugMode ? 'development' : 'production';
  static const bool isDebug = kDebugMode;
  static const bool isProduction = !kDebugMode;

  // API Configuration
  static const String baseApiUrl = 'https://api.videodownloader.pro';
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration downloadTimeout = Duration(minutes: 30);

  // Sentry Configuration
  static const String sentryDsn = 'YOUR_SENTRY_DSN_HERE';

  // Download Configuration
  static const int maxConcurrentDownloads = 3;
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 5);

  // Storage Configuration
  static const String downloadFolderName = 'VideoDownloader';
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB

  // UI Configuration
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 2);

  // Supported Platforms
  static const List<String> supportedPlatforms = [
    'youtube.com',
    'youtu.be',
    'tiktok.com',
    'instagram.com',
    'facebook.com',
    'twitter.com',
    'x.com',
  ];

  // Video Quality Options
  static const List<String> videoQualities = [
    '144p',
    '240p',
    '360p',
    '480p',
    '720p',
    '1080p',
    '1440p',
    '2160p',
  ];

  // Audio Quality Options
  static const List<String> audioQualities = [
    '64kbps',
    '128kbps',
    '192kbps',
    '256kbps',
    '320kbps',
  ];

  // File Formats
  static const List<String> videoFormats = ['mp4', 'webm', 'mkv'];
  static const List<String> audioFormats = ['mp3', 'aac', 'ogg', 'wav'];

  // Network Configuration
  static const Map<String, String> defaultHeaders = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': '*/*',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
  };
}
