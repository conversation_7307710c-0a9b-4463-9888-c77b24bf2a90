import 'package:flutter/material.dart';
import '../models/download_item.dart';
import '../models/video_info.dart';
import '../services/download_manager.dart';
import '../services/video_extractor.dart';
import '../services/platform_detector.dart';

class DownloadProvider extends ChangeNotifier {
  final DownloadManager _downloadManager = DownloadManager();
  
  List<DownloadItem> _downloads = [];
  VideoInfo? _currentVideoInfo;
  bool _isExtracting = false;
  bool _isLoading = false;
  String? _extractionError;
  
  // Getters
  List<DownloadItem> get downloads => _downloads;
  VideoInfo? get currentVideoInfo => _currentVideoInfo;
  bool get isExtracting => _isExtracting;
  bool get isLoading => _isLoading;
  String? get extractionError => _extractionError;
  
  // Filtered downloads
  List<DownloadItem> get activeDownloads => 
      _downloads.where((d) => d.status == DownloadStatus.downloading).toList();
  
  List<DownloadItem> get completedDownloads => 
      _downloads.where((d) => d.status == DownloadStatus.completed).toList();
  
  List<DownloadItem> get failedDownloads => 
      _downloads.where((d) => d.status == DownloadStatus.failed).toList();
  
  List<DownloadItem> get pausedDownloads => 
      _downloads.where((d) => d.status == DownloadStatus.paused).toList();

  DownloadProvider() {
    _loadDownloads();
  }

  Future<void> _loadDownloads() async {
    _setLoading(true);
    try {
      _downloads = await _downloadManager.getAllDownloads();
    } catch (e) {
      print('Error loading downloads: $e');
    }
    _setLoading(false);
    notifyListeners();
  }

  Future<void> refreshDownloads() async {
    await _loadDownloads();
  }

  Future<bool> extractVideoInfo(String url) async {
    if (url.trim().isEmpty) {
      _extractionError = 'URL cannot be empty';
      notifyListeners();
      return false;
    }

    if (!PlatformDetector.isValidUrl(url)) {
      _extractionError = 'Invalid URL format';
      notifyListeners();
      return false;
    }

    _setExtracting(true);
    _extractionError = null;
    _currentVideoInfo = null;

    try {
      final videoInfo = await VideoExtractor.extractVideoInfo(url);
      
      if (videoInfo != null) {
        _currentVideoInfo = videoInfo;
        _setExtracting(false);
        notifyListeners();
        return true;
      } else {
        _extractionError = 'Failed to extract video information';
        _setExtracting(false);
        notifyListeners();
        return false;
      }
    } catch (e) {
      _extractionError = 'Error: ${e.toString()}';
      _setExtracting(false);
      notifyListeners();
      return false;
    }
  }

  Future<String?> startDownload(VideoQuality selectedQuality) async {
    if (_currentVideoInfo == null) {
      return null;
    }

    try {
      final downloadId = await _downloadManager.startDownload(
        videoInfo: _currentVideoInfo!,
        selectedQuality: selectedQuality,
        onProgress: _onDownloadProgress,
      );

      await refreshDownloads();
      return downloadId;
    } catch (e) {
      print('Error starting download: $e');
      return null;
    }
  }

  void _onDownloadProgress(DownloadItem item) {
    final index = _downloads.indexWhere((d) => d.id == item.id);
    if (index != -1) {
      _downloads[index] = item;
      notifyListeners();
    }
  }

  Future<void> pauseDownload(String downloadId) async {
    await _downloadManager.pauseDownload(downloadId);
    await refreshDownloads();
  }

  Future<void> resumeDownload(String downloadId) async {
    await _downloadManager.resumeDownload(downloadId);
    await refreshDownloads();
  }

  Future<void> cancelDownload(String downloadId) async {
    await _downloadManager.cancelDownload(downloadId);
    await refreshDownloads();
  }

  Future<void> deleteDownload(String downloadId) async {
    await _downloadManager.deleteDownload(downloadId);
    await refreshDownloads();
  }

  Future<void> retryDownload(String downloadId) async {
    final download = _downloads.firstWhere((d) => d.id == downloadId);
    
    // Extract video info again
    final success = await extractVideoInfo(download.url);
    if (success && _currentVideoInfo != null) {
      // Find the same quality that was originally selected
      final originalQuality = _currentVideoInfo!.availableQualities
          .firstWhere(
            (q) => q.quality == download.quality && q.format == download.format,
            orElse: () => _currentVideoInfo!.availableQualities.first,
          );
      
      // Delete the failed download
      await deleteDownload(downloadId);
      
      // Start new download
      await startDownload(originalQuality);
    }
  }

  // Batch operations
  Future<void> pauseAllDownloads() async {
    for (final download in activeDownloads) {
      await _downloadManager.pauseDownload(download.id);
    }
    await refreshDownloads();
  }

  Future<void> resumeAllDownloads() async {
    for (final download in pausedDownloads) {
      await _downloadManager.resumeDownload(download.id);
    }
    await refreshDownloads();
  }

  Future<void> cancelAllDownloads() async {
    for (final download in activeDownloads) {
      await _downloadManager.cancelDownload(download.id);
    }
    await refreshDownloads();
  }

  Future<void> retryAllFailedDownloads() async {
    final failed = List<DownloadItem>.from(failedDownloads);
    for (final download in failed) {
      await retryDownload(download.id);
    }
  }

  Future<void> clearCompletedDownloads() async {
    await _downloadManager.clearCompletedDownloads();
    await refreshDownloads();
  }

  Future<void> clearAllDownloads() async {
    for (final download in _downloads) {
      await _downloadManager.deleteDownload(download.id);
    }
    await refreshDownloads();
  }

  // Search and filter
  List<DownloadItem> searchDownloads(String query) {
    if (query.trim().isEmpty) return _downloads;
    
    final lowerQuery = query.toLowerCase();
    return _downloads.where((download) {
      return download.title.toLowerCase().contains(lowerQuery) ||
             download.platform.toLowerCase().contains(lowerQuery) ||
             download.quality.toLowerCase().contains(lowerQuery) ||
             download.format.toLowerCase().contains(lowerQuery);
    }).toList();
  }

  List<DownloadItem> filterByStatus(DownloadStatus status) {
    return _downloads.where((d) => d.status == status).toList();
  }

  List<DownloadItem> filterByPlatform(String platform) {
    return _downloads.where((d) => d.platform == platform).toList();
  }

  List<DownloadItem> sortDownloads(String sortBy, {bool ascending = true}) {
    final sorted = List<DownloadItem>.from(_downloads);
    
    switch (sortBy) {
      case 'title':
        sorted.sort((a, b) => ascending 
            ? a.title.compareTo(b.title)
            : b.title.compareTo(a.title));
        break;
      case 'date':
        sorted.sort((a, b) => ascending 
            ? a.createdAt.compareTo(b.createdAt)
            : b.createdAt.compareTo(a.createdAt));
        break;
      case 'size':
        sorted.sort((a, b) {
          final aSize = a.totalBytes ?? 0;
          final bSize = b.totalBytes ?? 0;
          return ascending ? aSize.compareTo(bSize) : bSize.compareTo(aSize);
        });
        break;
      case 'platform':
        sorted.sort((a, b) => ascending 
            ? a.platform.compareTo(b.platform)
            : b.platform.compareTo(a.platform));
        break;
      case 'status':
        sorted.sort((a, b) => ascending 
            ? a.status.index.compareTo(b.status.index)
            : b.status.index.compareTo(a.status.index));
        break;
    }
    
    return sorted;
  }

  // Statistics
  Map<String, int> getDownloadStats() {
    return {
      'total': _downloads.length,
      'completed': completedDownloads.length,
      'downloading': activeDownloads.length,
      'failed': failedDownloads.length,
      'paused': pausedDownloads.length,
    };
  }

  Map<String, int> getPlatformStats() {
    final stats = <String, int>{};
    for (final download in _downloads) {
      stats[download.platform] = (stats[download.platform] ?? 0) + 1;
    }
    return stats;
  }

  int getTotalDownloadedBytes() {
    return completedDownloads
        .where((d) => d.totalBytes != null)
        .fold(0, (sum, d) => sum + d.totalBytes!);
  }

  // Helper methods
  void _setExtracting(bool extracting) {
    _isExtracting = extracting;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearCurrentVideoInfo() {
    _currentVideoInfo = null;
    _extractionError = null;
    notifyListeners();
  }

  void clearExtractionError() {
    _extractionError = null;
    notifyListeners();
  }

  // Get download by ID
  DownloadItem? getDownload(String downloadId) {
    try {
      return _downloads.firstWhere((d) => d.id == downloadId);
    } catch (e) {
      return null;
    }
  }

  // Check if URL is already being downloaded or completed
  bool isUrlAlreadyDownloaded(String url) {
    return _downloads.any((d) => 
        d.url == url && 
        (d.status == DownloadStatus.completed || 
         d.status == DownloadStatus.downloading));
  }

  @override
  void dispose() {
    _downloadManager.dispose();
    super.dispose();
  }
}
