class VideoQuality {
  final String quality;
  final String format;
  final String downloadUrl;
  final int? fileSize;
  final bool hasAudio;
  final bool hasVideo;

  VideoQuality({
    required this.quality,
    required this.format,
    required this.downloadUrl,
    this.fileSize,
    required this.hasAudio,
    required this.hasVideo,
  });

  factory VideoQuality.fromJson(Map<String, dynamic> json) {
    return VideoQuality(
      quality: json['quality'] ?? '',
      format: json['format'] ?? '',
      downloadUrl: json['download_url'] ?? '',
      fileSize: json['file_size'],
      hasAudio: json['has_audio'] ?? false,
      hasVideo: json['has_video'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'quality': quality,
      'format': format,
      'download_url': downloadUrl,
      'file_size': fileSize,
      'has_audio': hasAudio,
      'has_video': hasVideo,
    };
  }

  String get displayName {
    if (hasVideo && hasAudio) {
      return '$quality (Video + Audio)';
    } else if (hasVideo) {
      return '$quality (Video Only)';
    } else if (hasAudio) {
      return 'Audio Only ($quality)';
    }
    return quality;
  }

  @override
  String toString() {
    return 'VideoQuality(quality: $quality, format: $format, hasAudio: $hasAudio, hasVideo: $hasVideo)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoQuality &&
        other.quality == quality &&
        other.format == format &&
        other.downloadUrl == downloadUrl &&
        other.hasAudio == hasAudio &&
        other.hasVideo == hasVideo;
  }

  @override
  int get hashCode {
    return quality.hashCode ^
        format.hashCode ^
        downloadUrl.hashCode ^
        hasAudio.hashCode ^
        hasVideo.hashCode;
  }
}
