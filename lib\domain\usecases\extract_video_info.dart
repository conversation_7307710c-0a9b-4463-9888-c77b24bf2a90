import 'package:injectable/injectable.dart';

import '../../core/error/failures.dart';
import '../../core/utils/either.dart';
import '../entities/video_info.dart';
import '../repositories/video_repository.dart';

/// Use case for extracting video information from URL
@injectable
class ExtractVideoInfo {
  const ExtractVideoInfo(this._repository);

  final VideoRepository _repository;

  /// Execute the use case
  Future<Either<Failure, VideoInfo>> call(ExtractVideoInfoParams params) async {
    // Validate URL first
    final urlValidation = _repository.validateUrl(params.url);
    if (urlValidation.isLeft) {
      return Left(urlValidation.getLeftOrNull()!);
    }

    // Check if URL is supported
    if (!_repository.isUrlSupported(params.url)) {
      return const Left(
        ValidationFailure(
          message: 'URL is not supported',
          code: 'UNSUPPORTED_URL',
        ),
      );
    }

    // Try to get cached video info first
    if (params.useCache) {
      final cachedResult = await _repository.getCachedVideoInfo(params.url);
      if (cachedResult.isRight && cachedResult.getRightOrNull() != null) {
        return Right(cachedResult.getRightOrNull()!);
      }
    }

    // Extract video info from URL
    final result = await _repository.extractVideoInfo(params.url);
    
    // Cache the result if successful
    if (result.isRight && params.cacheResult) {
      await _repository.cacheVideoInfo(result.getRightOrNull()!);
    }

    return result;
  }
}

/// Parameters for ExtractVideoInfo use case
class ExtractVideoInfoParams {
  const ExtractVideoInfoParams({
    required this.url,
    this.useCache = true,
    this.cacheResult = true,
  });

  final String url;
  final bool useCache;
  final bool cacheResult;
}
