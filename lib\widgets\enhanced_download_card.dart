import 'package:flutter/material.dart';
import '../models/download_item.dart';
import '../utils/format_utils.dart';

class EnhancedDownloadCard extends StatelessWidget {
  final DownloadItem item;
  final VoidCallback? onRetry;
  final VoidCallback? onDelete;
  final VoidCallback? onPause;
  final VoidCallback? onResume;
  final VoidCallback? onCancel;

  const EnhancedDownloadCard({
    super.key,
    required this.item,
    this.onRetry,
    this.onDelete,
    this.onPause,
    this.onResume,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and platform
            Row(
              children: [
                // Thumbnail
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey[300],
                  ),
                  child: item.thumbnailUrl.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            item.thumbnailUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                _buildPlaceholderThumbnail(),
                          ),
                        )
                      : _buildPlaceholderThumbnail(),
                ),
                const SizedBox(width: 12),
                // Title and info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          _buildPlatformChip(),
                          const SizedBox(width: 8),
                          _buildQualityChip(),
                        ],
                      ),
                    ],
                  ),
                ),
                // Status indicator
                _buildStatusIndicator(),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Progress section
            if (item.status == DownloadStatus.downloading) ...[
              _buildProgressSection(),
              const SizedBox(height: 12),
            ],
            
            // Error message for failed downloads
            if (item.status == DownloadStatus.failed && item.errorMessage != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red[700], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        item.errorMessage!,
                        style: TextStyle(
                          color: Colors.red[700],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],
            
            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderThumbnail() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[300],
      ),
      child: Icon(
        Icons.video_library,
        color: Colors.grey[600],
        size: 30,
      ),
    );
  }

  Widget _buildPlatformChip() {
    Color chipColor;
    IconData chipIcon;
    
    switch (item.platform.toLowerCase()) {
      case 'youtube':
      case 'video platform 1':
        chipColor = Colors.red;
        chipIcon = Icons.play_circle_filled;
        break;
      case 'tiktok':
      case 'short video platform':
        chipColor = Colors.black;
        chipIcon = Icons.music_video;
        break;
      case 'instagram':
        chipColor = Colors.purple;
        chipIcon = Icons.camera_alt;
        break;
      case 'facebook':
        chipColor = Colors.blue;
        chipIcon = Icons.facebook;
        break;
      case 'twitter':
        chipColor = Colors.lightBlue;
        chipIcon = Icons.alternate_email;
        break;
      default:
        chipColor = Colors.grey;
        chipIcon = Icons.video_file;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(chipIcon, size: 14, color: chipColor),
          const SizedBox(width: 4),
          Text(
            item.platform,
            style: TextStyle(
              color: chipColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQualityChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withOpacity(0.3)),
      ),
      child: Text(
        '${item.quality} ${item.format.toUpperCase()}',
        style: const TextStyle(
          color: Colors.blue,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildStatusIndicator() {
    Color statusColor;
    IconData statusIcon;
    
    switch (item.status) {
      case DownloadStatus.completed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case DownloadStatus.downloading:
        statusColor = Colors.blue;
        statusIcon = Icons.download;
        break;
      case DownloadStatus.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      case DownloadStatus.paused:
        statusColor = Colors.orange;
        statusIcon = Icons.pause_circle;
        break;
      case DownloadStatus.cancelled:
        statusColor = Colors.grey;
        statusIcon = Icons.cancel;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.pending;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        statusIcon,
        color: statusColor,
        size: 24,
      ),
    );
  }

  Widget _buildProgressSection() {
    final progress = item.progress;
    final downloadedMB = (item.downloadedBytes / (1024 * 1024));
    final totalMB = item.totalBytes != null ? (item.totalBytes! / (1024 * 1024)) : 0;
    
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'تحميل...',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            Text(
              totalMB > 0 
                  ? '${downloadedMB.toStringAsFixed(1)} / ${totalMB.toStringAsFixed(1)} MB'
                  : '${downloadedMB.toStringAsFixed(1)} MB',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${(progress * 100).toStringAsFixed(1)}%',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
            if (totalMB > 0)
              Text(
                'متبقي ${(totalMB - downloadedMB).toStringAsFixed(1)} MB',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Retry button for failed downloads
        if (item.status == DownloadStatus.failed && onRetry != null)
          TextButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh, size: 18),
            label: const Text('إعادة المحاولة'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.blue,
            ),
          ),
        
        // Pause/Resume for active downloads
        if (item.status == DownloadStatus.downloading && onPause != null)
          TextButton.icon(
            onPressed: onPause,
            icon: const Icon(Icons.pause, size: 18),
            label: const Text('إيقاف'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.orange,
            ),
          ),
        
        if (item.status == DownloadStatus.paused && onResume != null)
          TextButton.icon(
            onPressed: onResume,
            icon: const Icon(Icons.play_arrow, size: 18),
            label: const Text('استكمال'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.green,
            ),
          ),
        
        // Cancel for active downloads
        if ((item.status == DownloadStatus.downloading || 
             item.status == DownloadStatus.paused) && onCancel != null)
          TextButton.icon(
            onPressed: onCancel,
            icon: const Icon(Icons.close, size: 18),
            label: const Text('إلغاء'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
          ),
        
        // Delete button
        if (onDelete != null)
          TextButton.icon(
            onPressed: onDelete,
            icon: const Icon(Icons.delete, size: 18),
            label: const Text('حذف'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
          ),
      ],
    );
  }
}
