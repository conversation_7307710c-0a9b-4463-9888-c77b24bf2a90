import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/download_provider.dart';
import '../providers/theme_provider.dart';

class DownloadStatsCard extends StatelessWidget {
  const DownloadStatsCard({super.key});

  @override
  Widget build(BuildContext context) {
    final downloadProvider = Provider.of<DownloadProvider>(context);
    final stats = downloadProvider.getDownloadStats();
    
    if (stats['total'] == 0) {
      return const SizedBox.shrink();
    }

    return Card(
      margin: const EdgeInsets.all(8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Download Statistics',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Stats grid
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total',
                    '${stats['total']}',
                    Icons.list,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Active',
                    '${stats['downloading']}',
                    Icons.download,
                    ThemeProvider.primaryBlue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Completed',
                    '${stats['completed']}',
                    Icons.check_circle,
                    ThemeProvider.accentGreen,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Failed',
                    '${stats['failed']}',
                    Icons.error,
                    ThemeProvider.errorRed,
                  ),
                ),
              ],
            ),
            
            // Progress bar for active downloads
            if (stats['downloading']! > 0) ...[
              const SizedBox(height: 16),
              _buildOverallProgress(downloadProvider),
            ],
            
            // Storage usage
            const SizedBox(height: 12),
            _buildStorageInfo(downloadProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        
        const SizedBox(height: 2),
        
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildOverallProgress(DownloadProvider downloadProvider) {
    final activeDownloads = downloadProvider.activeDownloads;
    
    if (activeDownloads.isEmpty) {
      return const SizedBox.shrink();
    }

    // Calculate overall progress
    double totalProgress = 0;
    for (final download in activeDownloads) {
      totalProgress += download.progress;
    }
    final averageProgress = totalProgress / activeDownloads.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Overall Progress',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${(averageProgress * 100).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: ThemeProvider.primaryBlue,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        LinearProgressIndicator(
          value: averageProgress,
          backgroundColor: Colors.grey.withOpacity(0.2),
          valueColor: const AlwaysStoppedAnimation<Color>(ThemeProvider.primaryBlue),
        ),
        
        const SizedBox(height: 4),
        
        Text(
          '${activeDownloads.length} download${activeDownloads.length > 1 ? 's' : ''} in progress',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildStorageInfo(DownloadProvider downloadProvider) {
    final totalBytes = downloadProvider.getTotalDownloadedBytes();
    final completedCount = downloadProvider.completedDownloads.length;
    
    return Row(
      children: [
        Icon(
          Icons.storage,
          size: 16,
          color: Colors.grey[600],
        ),
        
        const SizedBox(width: 8),
        
        Expanded(
          child: Text(
            'Storage used: ${_formatBytes(totalBytes)} • $completedCount file${completedCount != 1 ? 's' : ''}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ),
        
        // Quick actions
        PopupMenuButton<String>(
          icon: Icon(
            Icons.more_horiz,
            size: 16,
            color: Colors.grey[600],
          ),
          onSelected: (action) => _handleQuickAction(action, downloadProvider),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'refresh',
              child: Row(
                children: [
                  Icon(Icons.refresh, size: 16),
                  SizedBox(width: 8),
                  Text('Refresh'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'pause_all',
              child: Row(
                children: [
                  Icon(Icons.pause, size: 16),
                  SizedBox(width: 8),
                  Text('Pause All'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'resume_all',
              child: Row(
                children: [
                  Icon(Icons.play_arrow, size: 16),
                  SizedBox(width: 8),
                  Text('Resume All'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _handleQuickAction(String action, DownloadProvider downloadProvider) {
    switch (action) {
      case 'refresh':
        downloadProvider.refreshDownloads();
        break;
      case 'pause_all':
        downloadProvider.pauseAllDownloads();
        break;
      case 'resume_all':
        downloadProvider.resumeAllDownloads();
        break;
    }
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}
