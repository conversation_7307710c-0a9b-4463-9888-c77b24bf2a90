import '../models/video_info.dart';
import '../models/video_quality.dart';
import '../services/platform_detector.dart';

/// Helper class to fix common download issues
class DownloadFixHelper {
  /// Fixes video info with missing or invalid data
  static VideoInfo? fixVideoInfo(VideoInfo? videoInfo) {
    if (videoInfo == null) return null;

    // Fix empty or invalid titles
    String fixedTitle = _fixTitle(videoInfo.title, videoInfo.platform);

    // Fix missing thumbnails
    String fixedThumbnail = _fixThumbnail(
      videoInfo.thumbnailUrl,
      videoInfo.url,
      videoInfo.platform,
    );

    // Fix invalid qualities
    List<VideoQuality> fixedQualities = _fixQualities(
      videoInfo.availableQualities,
    );

    // If no valid qualities, return null
    if (fixedQualities.isEmpty) {
      return null;
    }

    // Fix duration
    Duration fixedDuration = videoInfo.duration ?? const Duration(seconds: 180);

    return VideoInfo(
      id:
          videoInfo.id.isNotEmpty
              ? videoInfo.id
              : DateTime.now().millisecondsSinceEpoch.toString(),
      title: fixedTitle,
      url: videoInfo.url,
      thumbnailUrl: fixedThumbnail,
      platform: videoInfo.platform,
      duration: fixedDuration,
      availableQualities: fixedQualities,
      description: videoInfo.description,
      uploader: videoInfo.uploader,
      uploadDate: videoInfo.uploadDate,
    );
  }

  /// Fixes video title
  static String _fixTitle(String title, String platform) {
    if (title.isEmpty ||
        title == 'Downloaded Video' ||
        title == 'Unknown' ||
        title.toLowerCase().contains('untitled')) {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return '$platform Video $timestamp';
    }

    // Clean up title - remove invalid characters
    String cleanTitle =
        title
            .replaceAll(RegExp(r'[<>:"/\\|?*]'), '') // Invalid filename chars
            .replaceAll(RegExp(r'\s+'), ' ') // Multiple spaces
            .trim();

    // Limit length
    if (cleanTitle.length > 100) {
      cleanTitle = '${cleanTitle.substring(0, 97)}...';
    }

    return cleanTitle.isNotEmpty ? cleanTitle : '$platform Video';
  }

  /// Fixes thumbnail URL
  static String _fixThumbnail(
    String thumbnailUrl,
    String videoUrl,
    String platform,
  ) {
    // If we have a valid thumbnail, use it
    if (thumbnailUrl.isNotEmpty &&
        !thumbnailUrl.contains('placeholder') &&
        thumbnailUrl.startsWith('http')) {
      return thumbnailUrl;
    }

    // Generate platform-specific thumbnail
    final platformType = PlatformDetector.detectPlatform(videoUrl);
    final videoId = PlatformDetector.extractVideoId(videoUrl, platformType);

    switch (platformType) {
      case SupportedPlatform.youtube:
        if (videoId != null && videoId.isNotEmpty) {
          return 'https://img.youtube.com/vi/$videoId/hqdefault.jpg';
        }
        return 'https://via.placeholder.com/480x360/FF0000/FFFFFF?text=YouTube';

      case SupportedPlatform.tiktok:
        return 'https://via.placeholder.com/480x640/FF0050/FFFFFF?text=TikTok';

      case SupportedPlatform.instagram:
        return 'https://via.placeholder.com/480x480/E4405F/FFFFFF?text=Instagram';

      case SupportedPlatform.facebook:
        return 'https://via.placeholder.com/480x360/1877F2/FFFFFF?text=Facebook';

      case SupportedPlatform.twitter:
        return 'https://via.placeholder.com/480x360/1DA1F2/FFFFFF?text=Twitter';

      default:
        return 'https://via.placeholder.com/480x360/666666/FFFFFF?text=Video';
    }
  }

  /// Fixes video qualities by removing invalid ones
  static List<VideoQuality> _fixQualities(List<VideoQuality> qualities) {
    return qualities.where((quality) {
      // Check if download URL is valid
      if (quality.downloadUrl.isEmpty) return false;
      if (!quality.downloadUrl.startsWith('http')) return false;
      if (quality.downloadUrl.contains('placeholder')) return false;

      // Check if quality info is valid
      if (quality.quality.isEmpty) return false;
      if (quality.format.isEmpty) return false;

      return true;
    }).toList();
  }

  /// Generates fallback video info when extraction fails
  static VideoInfo generateFallbackVideoInfo(String url) {
    final platform = PlatformDetector.detectPlatform(url);
    final platformName = PlatformDetector.getPlatformName(platform);
    final videoId =
        PlatformDetector.extractVideoId(url, platform) ??
        DateTime.now().millisecondsSinceEpoch.toString();

    return VideoInfo(
      id: videoId,
      title: '$platformName Video ${DateTime.now().millisecondsSinceEpoch}',
      url: url,
      thumbnailUrl: _fixThumbnail('', url, platformName),
      platform: platformName,
      duration: const Duration(seconds: 180),
      availableQualities: [
        VideoQuality(
          quality: '720p',
          format: 'mp4',
          downloadUrl: url, // Will be processed by download manager
          hasAudio: true,
          hasVideo: true,
        ),
      ],
      uploadDate: DateTime.now().subtract(const Duration(days: 1)),
    );
  }

  /// Validates if video info has minimum required data for download
  static bool isValidForDownload(VideoInfo videoInfo) {
    // Must have basic info
    if (videoInfo.id.isEmpty) return false;
    if (videoInfo.title.isEmpty) return false;
    if (videoInfo.url.isEmpty) return false;

    // Must have at least one valid quality
    final validQualities = _fixQualities(videoInfo.availableQualities);
    return validQualities.isNotEmpty;
  }

  /// Gets the best quality available for download
  static VideoQuality? getBestQuality(List<VideoQuality> qualities) {
    final validQualities = _fixQualities(qualities);
    if (validQualities.isEmpty) return null;

    // Prefer video over audio-only
    final videoQualities = validQualities.where((q) => q.hasVideo).toList();
    if (videoQualities.isNotEmpty) {
      // Sort by quality preference
      videoQualities.sort((a, b) {
        final aQuality = _getQualityScore(a.quality);
        final bQuality = _getQualityScore(b.quality);
        return bQuality.compareTo(aQuality); // Descending order
      });
      return videoQualities.first;
    }

    // Return first available quality if no video qualities
    return validQualities.first;
  }

  /// Gets quality score for sorting
  static int _getQualityScore(String quality) {
    final qualityLower = quality.toLowerCase();
    if (qualityLower.contains('1080') || qualityLower.contains('hd'))
      return 1080;
    if (qualityLower.contains('720')) return 720;
    if (qualityLower.contains('480')) return 480;
    if (qualityLower.contains('360')) return 360;
    if (qualityLower.contains('240')) return 240;
    return 0; // Unknown quality
  }
}
