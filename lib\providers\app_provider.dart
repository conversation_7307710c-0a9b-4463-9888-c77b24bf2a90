import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppProvider extends ChangeNotifier {
  static const String _languageKey = 'language';
  static const String _firstLaunchKey = 'first_launch';
  static const String _onboardingCompletedKey = 'onboarding_completed';
  
  Locale _currentLocale = const Locale('en', 'US');
  bool _isFirstLaunch = true;
  bool _onboardingCompleted = false;
  bool _isLoading = false;

  // Getters
  Locale get currentLocale => _currentLocale;
  bool get isFirstLaunch => _isFirstLaunch;
  bool get onboardingCompleted => _onboardingCompleted;
  bool get isLoading => _isLoading;
  bool get isArabic => _currentLocale.languageCode == 'ar';

  AppProvider() {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    _setLoading(true);
    
    final prefs = await SharedPreferences.getInstance();
    
    // Load language
    final languageCode = prefs.getString(_languageKey) ?? 'en';
    _currentLocale = Locale(languageCode, languageCode == 'ar' ? 'SA' : 'US');
    
    // Load first launch status
    _isFirstLaunch = prefs.getBool(_firstLaunchKey) ?? true;
    
    // Load onboarding status
    _onboardingCompleted = prefs.getBool(_onboardingCompletedKey) ?? false;
    
    _setLoading(false);
    notifyListeners();
  }

  Future<void> setLanguage(String languageCode) async {
    _currentLocale = Locale(languageCode, languageCode == 'ar' ? 'SA' : 'US');
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
    
    notifyListeners();
  }

  Future<void> setFirstLaunchCompleted() async {
    _isFirstLaunch = false;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_firstLaunchKey, false);
    
    notifyListeners();
  }

  Future<void> setOnboardingCompleted() async {
    _onboardingCompleted = true;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_onboardingCompletedKey, true);
    
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // App-wide settings
  static const Map<String, String> supportedLanguages = {
    'en': 'English',
    'ar': 'العربية',
  };

  // Get localized strings (basic implementation)
  String getString(String key) {
    final strings = isArabic ? _arabicStrings : _englishStrings;
    return strings[key] ?? key;
  }

  // English strings
  static const Map<String, String> _englishStrings = {
    'app_name': 'Video Downloader Pro',
    'home': 'Home',
    'downloads': 'Downloads',
    'settings': 'Settings',
    'paste_url': 'Paste video URL here...',
    'download': 'Download',
    'downloading': 'Downloading...',
    'completed': 'Completed',
    'failed': 'Failed',
    'paused': 'Paused',
    'cancelled': 'Cancelled',
    'quality': 'Quality',
    'format': 'Format',
    'file_size': 'File Size',
    'progress': 'Progress',
    'platform': 'Platform',
    'title': 'Title',
    'duration': 'Duration',
    'uploader': 'Uploader',
    'upload_date': 'Upload Date',
    'description': 'Description',
    'thumbnail': 'Thumbnail',
    'audio_only': 'Audio Only',
    'video_audio': 'Video + Audio',
    'video_only': 'Video Only',
    'high_quality': 'High Quality',
    'medium_quality': 'Medium Quality',
    'low_quality': 'Low Quality',
    'permissions_required': 'Permissions Required',
    'storage_permission': 'Storage Permission',
    'storage_permission_desc': 'Required to save downloaded videos',
    'notification_permission': 'Notification Permission',
    'notification_permission_desc': 'Shows download progress and completion',
    'grant_permission': 'Grant Permission',
    'skip': 'Skip',
    'next': 'Next',
    'finish': 'Finish',
    'get_started': 'Get Started',
    'welcome': 'Welcome',
    'welcome_desc': 'Download videos from your favorite platforms',
    'features': 'Features',
    'feature_1': 'Download from multiple platforms',
    'feature_2': 'Choose video quality',
    'feature_3': 'Audio-only downloads',
    'feature_4': 'Background downloads',
    'feature_5': 'No watermarks',
    'dark_mode': 'Dark Mode',
    'language': 'Language',
    'about': 'About',
    'version': 'Version',
    'privacy_policy': 'Privacy Policy',
    'terms_of_service': 'Terms of Service',
    'contact_us': 'Contact Us',
    'rate_app': 'Rate App',
    'share_app': 'Share App',
    'clear_downloads': 'Clear Downloads',
    'retry_failed': 'Retry Failed',
    'pause_all': 'Pause All',
    'resume_all': 'Resume All',
    'cancel_all': 'Cancel All',
    'delete': 'Delete',
    'retry': 'Retry',
    'pause': 'Pause',
    'resume': 'Resume',
    'cancel': 'Cancel',
    'play': 'Play',
    'share': 'Share',
    'info': 'Info',
    'error': 'Error',
    'success': 'Success',
    'warning': 'Warning',
    'confirm': 'Confirm',
    'yes': 'Yes',
    'no': 'No',
    'ok': 'OK',
    'close': 'Close',
    'save': 'Save',
    'edit': 'Edit',
    'search': 'Search',
    'filter': 'Filter',
    'sort': 'Sort',
    'refresh': 'Refresh',
    'loading': 'Loading...',
    'no_downloads': 'No downloads yet',
    'no_downloads_desc': 'Start by pasting a video URL',
    'invalid_url': 'Invalid URL',
    'unsupported_platform': 'Unsupported platform',
    'network_error': 'Network error',
    'download_error': 'Download error',
    'permission_denied': 'Permission denied',
    'storage_full': 'Storage full',
    'file_exists': 'File already exists',
    'download_started': 'Download started',
    'download_completed': 'Download completed',
    'download_failed': 'Download failed',
    'download_cancelled': 'Download cancelled',
    'download_paused': 'Download paused',
    'download_resumed': 'Download resumed',
  };

  // Arabic strings
  static const Map<String, String> _arabicStrings = {
    'app_name': 'محمل الفيديوهات المحترف',
    'home': 'الرئيسية',
    'downloads': 'التحميلات',
    'settings': 'الإعدادات',
    'paste_url': 'الصق رابط الفيديو هنا...',
    'download': 'تحميل',
    'downloading': 'جاري التحميل...',
    'completed': 'مكتمل',
    'failed': 'فشل',
    'paused': 'متوقف',
    'cancelled': 'ملغي',
    'quality': 'الجودة',
    'format': 'التنسيق',
    'file_size': 'حجم الملف',
    'progress': 'التقدم',
    'platform': 'المنصة',
    'title': 'العنوان',
    'duration': 'المدة',
    'uploader': 'الناشر',
    'upload_date': 'تاريخ النشر',
    'description': 'الوصف',
    'thumbnail': 'الصورة المصغرة',
    'audio_only': 'صوت فقط',
    'video_audio': 'فيديو + صوت',
    'video_only': 'فيديو فقط',
    'high_quality': 'جودة عالية',
    'medium_quality': 'جودة متوسطة',
    'low_quality': 'جودة منخفضة',
    'permissions_required': 'أذونات مطلوبة',
    'storage_permission': 'إذن التخزين',
    'storage_permission_desc': 'مطلوب لحفظ الفيديوهات المحملة',
    'notification_permission': 'إذن الإشعارات',
    'notification_permission_desc': 'يظهر تقدم التحميل والإكمال',
    'grant_permission': 'منح الإذن',
    'skip': 'تخطي',
    'next': 'التالي',
    'finish': 'إنهاء',
    'get_started': 'ابدأ الآن',
    'welcome': 'مرحباً',
    'welcome_desc': 'حمل الفيديوهات من منصاتك المفضلة',
    'features': 'المميزات',
    'feature_1': 'التحميل من منصات متعددة',
    'feature_2': 'اختيار جودة الفيديو',
    'feature_3': 'تحميل الصوت فقط',
    'feature_4': 'التحميل في الخلفية',
    'feature_5': 'بدون علامات مائية',
    'dark_mode': 'الوضع الليلي',
    'language': 'اللغة',
    'about': 'حول',
    'version': 'الإصدار',
    'privacy_policy': 'سياسة الخصوصية',
    'terms_of_service': 'شروط الخدمة',
    'contact_us': 'اتصل بنا',
    'rate_app': 'قيم التطبيق',
    'share_app': 'شارك التطبيق',
    'clear_downloads': 'مسح التحميلات',
    'retry_failed': 'إعادة المحاولة للفاشلة',
    'pause_all': 'إيقاف الكل',
    'resume_all': 'استئناف الكل',
    'cancel_all': 'إلغاء الكل',
    'delete': 'حذف',
    'retry': 'إعادة المحاولة',
    'pause': 'إيقاف',
    'resume': 'استئناف',
    'cancel': 'إلغاء',
    'play': 'تشغيل',
    'share': 'مشاركة',
    'info': 'معلومات',
    'error': 'خطأ',
    'success': 'نجح',
    'warning': 'تحذير',
    'confirm': 'تأكيد',
    'yes': 'نعم',
    'no': 'لا',
    'ok': 'موافق',
    'close': 'إغلاق',
    'save': 'حفظ',
    'edit': 'تعديل',
    'search': 'بحث',
    'filter': 'تصفية',
    'sort': 'ترتيب',
    'refresh': 'تحديث',
    'loading': 'جاري التحميل...',
    'no_downloads': 'لا توجد تحميلات بعد',
    'no_downloads_desc': 'ابدأ بلصق رابط فيديو',
    'invalid_url': 'رابط غير صحيح',
    'unsupported_platform': 'منصة غير مدعومة',
    'network_error': 'خطأ في الشبكة',
    'download_error': 'خطأ في التحميل',
    'permission_denied': 'تم رفض الإذن',
    'storage_full': 'التخزين ممتلئ',
    'file_exists': 'الملف موجود بالفعل',
    'download_started': 'بدأ التحميل',
    'download_completed': 'اكتمل التحميل',
    'download_failed': 'فشل التحميل',
    'download_cancelled': 'تم إلغاء التحميل',
    'download_paused': 'تم إيقاف التحميل',
    'download_resumed': 'تم استئناف التحميل',
  };
}
