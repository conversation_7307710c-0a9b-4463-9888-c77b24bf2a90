import 'package:injectable/injectable.dart';

import '../../core/error/failures.dart';
import '../../core/utils/either.dart';
import '../entities/download_item.dart';
import '../repositories/download_repository.dart';

/// Use case for getting downloads
@injectable
class GetDownloads {
  const GetDownloads(this._repository);

  final DownloadRepository _repository;

  /// Get all downloads
  Future<Either<Failure, List<DownloadItem>>> getAllDownloads() async {
    return await _repository.getAllDownloads();
  }

  /// Get downloads by status
  Future<Either<Failure, List<DownloadItem>>> getDownloadsByStatus(
    DownloadStatus status,
  ) async {
    return await _repository.getDownloadsByStatus(status);
  }

  /// Get active downloads
  Future<Either<Failure, List<DownloadItem>>> getActiveDownloads() async {
    return await _repository.getActiveDownloads();
  }

  /// Get completed downloads
  Future<Either<Failure, List<DownloadItem>>> getCompletedDownloads() async {
    return await _repository.getCompletedDownloads();
  }

  /// Get failed downloads
  Future<Either<Failure, List<DownloadItem>>> getFailedDownloads() async {
    return await _repository.getFailedDownloads();
  }

  /// Get download by ID
  Future<Either<Failure, DownloadItem?>> getDownloadById(String downloadId) async {
    return await _repository.getDownloadById(downloadId);
  }

  /// Watch all downloads
  Stream<List<DownloadItem>> watchAllDownloads() {
    return _repository.watchAllDownloads();
  }

  /// Watch download progress
  Stream<DownloadItem> watchDownloadProgress(String downloadId) {
    return _repository.watchDownloadProgress(downloadId);
  }
}
