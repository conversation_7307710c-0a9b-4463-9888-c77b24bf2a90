import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/video_info.dart';
import 'platform_detector.dart';

class VideoExtractor {
  static const String _baseApiUrl = 'https://api.cobalt.tools/api/json';
  
  // Alternative APIs for different platforms
  static const Map<SupportedPlatform, List<String>> _alternativeApis = {
    SupportedPlatform.youtube: [
      'https://api.cobalt.tools/api/json',
      'https://api.savefrom.net/info',
    ],
    SupportedPlatform.tiktok: [
      'https://api.cobalt.tools/api/json',
      'https://tikwm.com/api/',
    ],
    SupportedPlatform.instagram: [
      'https://api.cobalt.tools/api/json',
    ],
  };

  static Future<VideoInfo?> extractVideoInfo(String url) async {
    final platform = PlatformDetector.detectPlatform(url);
    
    if (platform == SupportedPlatform.unknown) {
      throw Exception('Unsupported platform');
    }

    // Try different extraction methods based on platform
    switch (platform) {
      case SupportedPlatform.youtube:
        return await _extractYouTubeInfo(url);
      case SupportedPlatform.tiktok:
        return await _extractTikTokInfo(url);
      case SupportedPlatform.instagram:
        return await _extractInstagramInfo(url);
      case SupportedPlatform.facebook:
        return await _extractFacebookInfo(url);
      case SupportedPlatform.twitter:
        return await _extractTwitterInfo(url);
      default:
        return await _extractGenericInfo(url, platform);
    }
  }

  static Future<VideoInfo?> _extractYouTubeInfo(String url) async {
    try {
      // Method 1: Using Cobalt API
      final cobaltResult = await _extractWithCobalt(url);
      if (cobaltResult != null) return cobaltResult;

      // Method 2: Using yt-dlp style extraction
      final ytDlpResult = await _extractWithYtDlp(url);
      if (ytDlpResult != null) return ytDlpResult;

      // Method 3: Direct extraction (fallback)
      return await _extractYouTubeDirect(url);
    } catch (e) {
      print('YouTube extraction error: $e');
      return null;
    }
  }

  static Future<VideoInfo?> _extractTikTokInfo(String url) async {
    try {
      // Method 1: Using TikWM API (no watermark)
      final tikwmResult = await _extractTikTokWithTikWM(url);
      if (tikwmResult != null) return tikwmResult;

      // Method 2: Using Cobalt API
      final cobaltResult = await _extractWithCobalt(url);
      if (cobaltResult != null) return cobaltResult;

      // Method 3: Direct extraction
      return await _extractTikTokDirect(url);
    } catch (e) {
      print('TikTok extraction error: $e');
      return null;
    }
  }

  static Future<VideoInfo?> _extractInstagramInfo(String url) async {
    try {
      // Method 1: Using Cobalt API
      final cobaltResult = await _extractWithCobalt(url);
      if (cobaltResult != null) return cobaltResult;

      // Method 2: Direct extraction
      return await _extractInstagramDirect(url);
    } catch (e) {
      print('Instagram extraction error: $e');
      return null;
    }
  }

  static Future<VideoInfo?> _extractFacebookInfo(String url) async {
    try {
      return await _extractWithCobalt(url);
    } catch (e) {
      print('Facebook extraction error: $e');
      return null;
    }
  }

  static Future<VideoInfo?> _extractTwitterInfo(String url) async {
    try {
      return await _extractWithCobalt(url);
    } catch (e) {
      print('Twitter extraction error: $e');
      return null;
    }
  }

  static Future<VideoInfo?> _extractGenericInfo(String url, SupportedPlatform platform) async {
    try {
      return await _extractWithCobalt(url);
    } catch (e) {
      print('Generic extraction error: $e');
      return null;
    }
  }

  // Cobalt API extraction (supports multiple platforms)
  static Future<VideoInfo?> _extractWithCobalt(String url) async {
    try {
      final response = await http.post(
        Uri.parse(_baseApiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'url': url,
          'vQuality': '720',
          'vCodec': 'h264',
          'vFormat': 'mp4',
          'aFormat': 'mp3',
          'isAudioOnly': false,
          'isNoTTWatermark': true,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseCobaltResponse(data, url);
      }
    } catch (e) {
      print('Cobalt API error: $e');
    }
    return null;
  }

  // TikWM API for TikTok (no watermark)
  static Future<VideoInfo?> _extractTikTokWithTikWM(String url) async {
    try {
      final response = await http.post(
        Uri.parse('https://tikwm.com/api/'),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'url=$url&hd=1',
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseTikWMResponse(data, url);
      }
    } catch (e) {
      print('TikWM API error: $e');
    }
    return null;
  }

  // YouTube direct extraction (simplified)
  static Future<VideoInfo?> _extractYouTubeDirect(String url) async {
    try {
      final videoId = PlatformDetector.extractVideoId(url, SupportedPlatform.youtube);
      if (videoId == null) return null;

      // This is a simplified version - in production, you'd use more sophisticated methods
      final response = await http.get(
        Uri.parse('https://www.youtube.com/watch?v=$videoId'),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      );

      if (response.statusCode == 200) {
        return _parseYouTubeHtml(response.body, videoId, url);
      }
    } catch (e) {
      print('YouTube direct extraction error: $e');
    }
    return null;
  }

  // TikTok direct extraction
  static Future<VideoInfo?> _extractTikTokDirect(String url) async {
    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      );

      if (response.statusCode == 200) {
        return _parseTikTokHtml(response.body, url);
      }
    } catch (e) {
      print('TikTok direct extraction error: $e');
    }
    return null;
  }

  // Instagram direct extraction
  static Future<VideoInfo?> _extractInstagramDirect(String url) async {
    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      );

      if (response.statusCode == 200) {
        return _parseInstagramHtml(response.body, url);
      }
    } catch (e) {
      print('Instagram direct extraction error: $e');
    }
    return null;
  }

  // yt-dlp style extraction (using public APIs)
  static Future<VideoInfo?> _extractWithYtDlp(String url) async {
    // This would integrate with yt-dlp or similar tools
    // For now, return null as this requires server-side implementation
    return null;
  }

  // Response parsers
  static VideoInfo? _parseCobaltResponse(Map<String, dynamic> data, String originalUrl) {
    try {
      final platform = PlatformDetector.detectPlatform(originalUrl);
      
      if (data['status'] == 'success' || data['status'] == 'redirect') {
        final qualities = <VideoQuality>[];
        
        if (data['url'] != null) {
          qualities.add(VideoQuality(
            quality: '720p',
            format: 'mp4',
            downloadUrl: data['url'],
            hasAudio: true,
            hasVideo: true,
          ));
        }

        return VideoInfo(
          id: data['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
          title: data['filename'] ?? 'Downloaded Video',
          url: originalUrl,
          thumbnailUrl: data['thumb'] ?? '',
          platform: PlatformDetector.getPlatformName(platform),
          duration: Duration(seconds: 0),
          availableQualities: qualities,
        );
      }
    } catch (e) {
      print('Error parsing Cobalt response: $e');
    }
    return null;
  }

  static VideoInfo? _parseTikWMResponse(Map<String, dynamic> data, String originalUrl) {
    try {
      if (data['code'] == 0 && data['data'] != null) {
        final videoData = data['data'];
        final qualities = <VideoQuality>[];
        
        if (videoData['hdplay'] != null) {
          qualities.add(VideoQuality(
            quality: 'HD',
            format: 'mp4',
            downloadUrl: videoData['hdplay'],
            hasAudio: true,
            hasVideo: true,
          ));
        }
        
        if (videoData['play'] != null) {
          qualities.add(VideoQuality(
            quality: 'SD',
            format: 'mp4',
            downloadUrl: videoData['play'],
            hasAudio: true,
            hasVideo: true,
          ));
        }

        if (videoData['music'] != null) {
          qualities.add(VideoQuality(
            quality: 'Audio',
            format: 'mp3',
            downloadUrl: videoData['music'],
            hasAudio: true,
            hasVideo: false,
          ));
        }

        return VideoInfo(
          id: videoData['id']?.toString() ?? DateTime.now().millisecondsSinceEpoch.toString(),
          title: videoData['title'] ?? 'TikTok Video',
          url: originalUrl,
          thumbnailUrl: videoData['cover'] ?? '',
          platform: 'Short Video Platform',
          duration: Duration(seconds: videoData['duration'] ?? 0),
          availableQualities: qualities,
          uploader: videoData['author']?['nickname'],
        );
      }
    } catch (e) {
      print('Error parsing TikWM response: $e');
    }
    return null;
  }

  static VideoInfo? _parseYouTubeHtml(String html, String videoId, String originalUrl) {
    try {
      // Basic HTML parsing - in production, use a proper HTML parser
      final titleMatch = RegExp(r'"title":"([^"]+)"').firstMatch(html);
      final title = titleMatch?.group(1) ?? 'YouTube Video';
      
      final thumbnailUrl = 'https://img.youtube.com/vi/$videoId/maxresdefault.jpg';
      
      // Create basic quality options
      final qualities = [
        VideoQuality(
          quality: '720p',
          format: 'mp4',
          downloadUrl: '', // Would be extracted from player response
          hasAudio: true,
          hasVideo: true,
        ),
        VideoQuality(
          quality: '480p',
          format: 'mp4',
          downloadUrl: '',
          hasAudio: true,
          hasVideo: true,
        ),
        VideoQuality(
          quality: 'Audio',
          format: 'mp3',
          downloadUrl: '',
          hasAudio: true,
          hasVideo: false,
        ),
      ];

      return VideoInfo(
        id: videoId,
        title: title,
        url: originalUrl,
        thumbnailUrl: thumbnailUrl,
        platform: 'Video Platform 1',
        duration: Duration(seconds: 0),
        availableQualities: qualities,
      );
    } catch (e) {
      print('Error parsing YouTube HTML: $e');
    }
    return null;
  }

  static VideoInfo? _parseTikTokHtml(String html, String originalUrl) {
    // Similar HTML parsing for TikTok
    return null;
  }

  static VideoInfo? _parseInstagramHtml(String html, String originalUrl) {
    // Similar HTML parsing for Instagram
    return null;
  }
}
