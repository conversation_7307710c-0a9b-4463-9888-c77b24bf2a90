import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/video_info.dart';
import '../models/video_quality.dart';
import 'platform_detector.dart';
import '../utils/download_fix_helper.dart';

class VideoExtractor {
  // Multiple API endpoints for reliability
  static const List<String> _cobaltInstances = [
    'https://api.cobalt.tools/api/json',
    'https://co.wuk.sh/api/json',
    'https://cobalt-api.org/api/json',
  ];

  static const List<String> _tikwmInstances = [
    'https://tikwm.com/api/',
    'https://www.tikwm.com/api/',
    'https://api.tikwm.com/api/',
  ];

  static const List<String> _snapSaveInstances = [
    'https://snapsave.app/action.php',
    'https://api.snapsave.app/action.php',
  ];

  // YouTube extraction APIs
  static const List<String> _youtubeApis = [
    'https://api.cobalt.tools/api/json',
    'https://yt1s.com/api/ajaxSearch/index',
    'https://www.y2mate.com/mates/en68/ajax/search',
  ];

  static Future<VideoInfo?> extractVideoInfo(String url) async {
    final platform = PlatformDetector.detectPlatform(url);

    if (platform == SupportedPlatform.unknown) {
      throw Exception('Unsupported platform');
    }

    // Try different extraction methods based on platform
    VideoInfo? videoInfo;
    switch (platform) {
      case SupportedPlatform.youtube:
        videoInfo = await _extractYouTubeInfo(url);
        break;
      case SupportedPlatform.tiktok:
        videoInfo = await _extractTikTokInfo(url);
        break;
      case SupportedPlatform.instagram:
        videoInfo = await _extractInstagramInfo(url);
        break;
      case SupportedPlatform.facebook:
        videoInfo = await _extractFacebookInfo(url);
        break;
      case SupportedPlatform.twitter:
        videoInfo = await _extractTwitterInfo(url);
        break;
      default:
        videoInfo = await _extractGenericInfo(url, platform);
    }

    // Fix and validate the extracted video info
    return DownloadFixHelper.fixVideoInfo(videoInfo);
  }

  static Future<VideoInfo?> _extractYouTubeInfo(String url) async {
    try {
      // Method 1: Using Y2mate API (most reliable for YouTube)
      final y2mateResult = await _extractYouTubeWithY2mate(url);
      if (y2mateResult != null) return y2mateResult;

      // Method 2: Using Cobalt API
      final cobaltResult = await _extractWithCobalt(url);
      if (cobaltResult != null) return cobaltResult;

      // Method 3: Using YT1S API
      final yt1sResult = await _extractYouTubeWithYT1S(url);
      if (yt1sResult != null) return yt1sResult;

      // Method 4: Direct extraction (fallback)
      return await _extractYouTubeDirect(url);
    } catch (e) {
      // Use debugPrint instead of print for production
      return null;
    }
  }

  static Future<VideoInfo?> _extractTikTokInfo(String url) async {
    try {
      // Method 1: Using TikWM API (no watermark)
      final tikwmResult = await _extractTikTokWithTikWM(url);
      if (tikwmResult != null) return tikwmResult;

      // Method 2: Using Cobalt API
      final cobaltResult = await _extractWithCobalt(url);
      if (cobaltResult != null) return cobaltResult;

      // Method 3: Direct extraction
      return await _extractTikTokDirect(url);
    } catch (e) {
      print('TikTok extraction error: $e');
      return null;
    }
  }

  static Future<VideoInfo?> _extractInstagramInfo(String url) async {
    try {
      // Method 1: Using SnapSave API (best for Instagram)
      final snapSaveResult = await _extractInstagramWithSnapSave(url);
      if (snapSaveResult != null) return snapSaveResult;

      // Method 2: Using Cobalt API
      final cobaltResult = await _extractWithCobalt(url);
      if (cobaltResult != null) return cobaltResult;

      // Method 3: Direct extraction
      return await _extractInstagramDirect(url);
    } catch (e) {
      return null;
    }
  }

  static Future<VideoInfo?> _extractFacebookInfo(String url) async {
    try {
      return await _extractWithCobalt(url);
    } catch (e) {
      print('Facebook extraction error: $e');
      return null;
    }
  }

  static Future<VideoInfo?> _extractTwitterInfo(String url) async {
    try {
      return await _extractWithCobalt(url);
    } catch (e) {
      print('Twitter extraction error: $e');
      return null;
    }
  }

  static Future<VideoInfo?> _extractGenericInfo(
    String url,
    SupportedPlatform platform,
  ) async {
    try {
      return await _extractWithCobalt(url);
    } catch (e) {
      print('Generic extraction error: $e');
      return null;
    }
  }

  // Cobalt API extraction (supports multiple platforms)
  static Future<VideoInfo?> _extractWithCobalt(String url) async {
    // Try multiple Cobalt instances for reliability
    for (final apiUrl in _cobaltInstances) {
      try {
        final response = await http
            .post(
              Uri.parse(apiUrl),
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              },
              body: jsonEncode({
                'url': url,
                'vQuality': '720',
                'vCodec': 'h264',
                'vFormat': 'mp4',
                'aFormat': 'mp3',
                'isAudioOnly': false,
                'isNoTTWatermark': true,
                'dubLang': false,
                'filenamePattern': 'classic',
              }),
            )
            .timeout(const Duration(seconds: 15));

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          final result = _parseCobaltResponse(data, url);
          if (result != null) return result;
        }
      } catch (e) {
        // Try next instance
        continue;
      }
    }
    return null;
  }

  // TikWM API for TikTok (no watermark)
  static Future<VideoInfo?> _extractTikTokWithTikWM(String url) async {
    // Try multiple TikWM instances
    for (final apiUrl in _tikwmInstances) {
      try {
        final response = await http
            .post(
              Uri.parse(apiUrl),
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              },
              body: 'url=${Uri.encodeComponent(url)}&hd=1',
            )
            .timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          final result = _parseTikWMResponse(data, url);
          if (result != null) return result;
        }
      } catch (e) {
        // Try next instance
        continue;
      }
    }
    return null;
  }

  // YouTube direct extraction (simplified)
  static Future<VideoInfo?> _extractYouTubeDirect(String url) async {
    try {
      final videoId = PlatformDetector.extractVideoId(
        url,
        SupportedPlatform.youtube,
      );
      if (videoId == null) return null;

      // This is a simplified version - in production, you'd use more sophisticated methods
      final response = await http.get(
        Uri.parse('https://www.youtube.com/watch?v=$videoId'),
        headers: {
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      );

      if (response.statusCode == 200) {
        return _parseYouTubeHtml(response.body, videoId, url);
      }
    } catch (e) {
      print('YouTube direct extraction error: $e');
    }
    return null;
  }

  // TikTok direct extraction
  static Future<VideoInfo?> _extractTikTokDirect(String url) async {
    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      );

      if (response.statusCode == 200) {
        return _parseTikTokHtml(response.body, url);
      }
    } catch (e) {
      print('TikTok direct extraction error: $e');
    }
    return null;
  }

  // Instagram direct extraction
  static Future<VideoInfo?> _extractInstagramDirect(String url) async {
    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      );

      if (response.statusCode == 200) {
        return _parseInstagramHtml(response.body, url);
      }
    } catch (e) {
      print('Instagram direct extraction error: $e');
    }
    return null;
  }

  // Y2mate API for YouTube
  static Future<VideoInfo?> _extractYouTubeWithY2mate(String url) async {
    try {
      final videoId = PlatformDetector.extractVideoId(
        url,
        SupportedPlatform.youtube,
      );
      if (videoId == null) return null;

      final response = await http
          .post(
            Uri.parse('https://www.y2mate.com/mates/en68/ajax/search'),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'User-Agent':
                  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            },
            body:
                'k_query=https://www.youtube.com/watch?v=$videoId&k_page=home&hl=en&q_auto=0',
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseY2mateResponse(data, url, videoId);
      }
    } catch (e) {
      // Continue to next method
    }
    return null;
  }

  // YT1S API for YouTube
  static Future<VideoInfo?> _extractYouTubeWithYT1S(String url) async {
    try {
      final videoId = PlatformDetector.extractVideoId(
        url,
        SupportedPlatform.youtube,
      );
      if (videoId == null) return null;

      final response = await http
          .post(
            Uri.parse('https://yt1s.com/api/ajaxSearch/index'),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'User-Agent':
                  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            },
            body: 'q=https://www.youtube.com/watch?v=$videoId&vt=home',
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return _parseYT1SResponse(data, url, videoId);
      }
    } catch (e) {
      // Continue to next method
    }
    return null;
  }

  // SnapSave API for Instagram
  static Future<VideoInfo?> _extractInstagramWithSnapSave(String url) async {
    for (final apiUrl in _snapSaveInstances) {
      try {
        final response = await http
            .post(
              Uri.parse(apiUrl),
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              },
              body: 'url=${Uri.encodeComponent(url)}',
            )
            .timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          final result = _parseSnapSaveResponse(data, url);
          if (result != null) return result;
        }
      } catch (e) {
        continue;
      }
    }
    return null;
  }

  // Response parsers
  static VideoInfo? _parseCobaltResponse(
    Map<String, dynamic> data,
    String originalUrl,
  ) {
    try {
      final platform = PlatformDetector.detectPlatform(originalUrl);
      final videoId =
          PlatformDetector.extractVideoId(originalUrl, platform) ??
          DateTime.now().millisecondsSinceEpoch.toString();

      if (data['status'] == 'success' || data['status'] == 'redirect') {
        final qualities = <VideoQuality>[];

        // Handle different response formats
        String downloadUrl = '';
        if (data['url'] != null) {
          downloadUrl = data['url'].toString();
        } else if (data['picker'] != null && data['picker'] is List) {
          final picker = data['picker'] as List;
          if (picker.isNotEmpty && picker[0]['url'] != null) {
            downloadUrl = picker[0]['url'].toString();
          }
        }

        if (downloadUrl.isNotEmpty) {
          // Add multiple quality options
          qualities.addAll([
            VideoQuality(
              quality: '720p',
              format: 'mp4',
              downloadUrl: downloadUrl,
              hasAudio: true,
              hasVideo: true,
            ),
            VideoQuality(
              quality: '480p',
              format: 'mp4',
              downloadUrl: downloadUrl,
              hasAudio: true,
              hasVideo: true,
            ),
            VideoQuality(
              quality: 'Audio',
              format: 'mp3',
              downloadUrl: downloadUrl,
              hasAudio: true,
              hasVideo: false,
            ),
          ]);
        }

        // Generate better title and thumbnail
        String title = 'Downloaded Video';
        String thumbnailUrl = '';

        if (data['filename'] != null &&
            data['filename'].toString().isNotEmpty) {
          title = data['filename'].toString();
        } else {
          title = _generateTitleFromUrl(originalUrl, platform);
        }

        thumbnailUrl = _generateThumbnailUrl(originalUrl, platform, videoId);

        return VideoInfo(
          id: videoId,
          title: title,
          url: originalUrl,
          thumbnailUrl: thumbnailUrl,
          platform: PlatformDetector.getPlatformName(platform),
          duration:
              _parseDuration(data['duration']) ?? const Duration(seconds: 180),
          availableQualities: qualities,
          description: data['description']?.toString(),
          uploadDate: DateTime.now().subtract(const Duration(days: 1)),
        );
      }
    } catch (e) {
      print('Error parsing Cobalt response: $e');
    }
    return null;
  }

  // Helper methods
  static String _generateTitleFromUrl(String url, SupportedPlatform platform) {
    final platformName = PlatformDetector.getPlatformName(platform);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '$platformName Video $timestamp';
  }

  static String _generateThumbnailUrl(
    String url,
    SupportedPlatform platform,
    String videoId,
  ) {
    switch (platform) {
      case SupportedPlatform.youtube:
        return 'https://img.youtube.com/vi/$videoId/hqdefault.jpg';
      case SupportedPlatform.tiktok:
        return 'https://via.placeholder.com/480x640/FF0050/FFFFFF?text=TikTok';
      case SupportedPlatform.instagram:
        return 'https://via.placeholder.com/480x480/E4405F/FFFFFF?text=Instagram';
      case SupportedPlatform.facebook:
        return 'https://via.placeholder.com/480x360/1877F2/FFFFFF?text=Facebook';
      case SupportedPlatform.twitter:
        return 'https://via.placeholder.com/480x360/1DA1F2/FFFFFF?text=Twitter';
      default:
        return 'https://via.placeholder.com/480x360/666666/FFFFFF?text=Video';
    }
  }

  static Duration? _parseDuration(dynamic durationData) {
    if (durationData == null) return null;

    if (durationData is int) {
      return Duration(seconds: durationData);
    } else if (durationData is String) {
      final parts = durationData.split(':');
      if (parts.length == 2) {
        final minutes = int.tryParse(parts[0]) ?? 0;
        final seconds = int.tryParse(parts[1]) ?? 0;
        return Duration(minutes: minutes, seconds: seconds);
      } else if (parts.length == 3) {
        final hours = int.tryParse(parts[0]) ?? 0;
        final minutes = int.tryParse(parts[1]) ?? 0;
        final seconds = int.tryParse(parts[2]) ?? 0;
        return Duration(hours: hours, minutes: minutes, seconds: seconds);
      }
    }

    return Duration(seconds: 180); // Default 3 minutes
  }

  static VideoInfo? _parseTikWMResponse(
    Map<String, dynamic> data,
    String originalUrl,
  ) {
    try {
      if (data['code'] == 0 && data['data'] != null) {
        final videoData = data['data'];
        final qualities = <VideoQuality>[];

        if (videoData['hdplay'] != null) {
          qualities.add(
            VideoQuality(
              quality: 'HD',
              format: 'mp4',
              downloadUrl: videoData['hdplay'],
              hasAudio: true,
              hasVideo: true,
            ),
          );
        }

        if (videoData['play'] != null) {
          qualities.add(
            VideoQuality(
              quality: 'SD',
              format: 'mp4',
              downloadUrl: videoData['play'],
              hasAudio: true,
              hasVideo: true,
            ),
          );
        }

        if (videoData['music'] != null) {
          qualities.add(
            VideoQuality(
              quality: 'Audio',
              format: 'mp3',
              downloadUrl: videoData['music'],
              hasAudio: true,
              hasVideo: false,
            ),
          );
        }

        return VideoInfo(
          id:
              videoData['id']?.toString() ??
              DateTime.now().millisecondsSinceEpoch.toString(),
          title: videoData['title'] ?? 'TikTok Video',
          url: originalUrl,
          thumbnailUrl: videoData['cover'] ?? '',
          platform: 'Short Video Platform',
          duration: Duration(seconds: videoData['duration'] ?? 0),
          availableQualities: qualities,
          uploader: videoData['author']?['nickname'],
        );
      }
    } catch (e) {
      print('Error parsing TikWM response: $e');
    }
    return null;
  }

  static VideoInfo? _parseYouTubeHtml(
    String html,
    String videoId,
    String originalUrl,
  ) {
    try {
      // Basic HTML parsing - in production, use a proper HTML parser
      final titleMatch = RegExp(r'"title":"([^"]+)"').firstMatch(html);
      final title = titleMatch?.group(1) ?? 'YouTube Video';

      // Try different thumbnail qualities
      final thumbnailUrl = 'https://img.youtube.com/vi/$videoId/hqdefault.jpg';

      // Create basic quality options
      final qualities = [
        VideoQuality(
          quality: '720p',
          format: 'mp4',
          downloadUrl: '', // Would be extracted from player response
          hasAudio: true,
          hasVideo: true,
        ),
        VideoQuality(
          quality: '480p',
          format: 'mp4',
          downloadUrl: '',
          hasAudio: true,
          hasVideo: true,
        ),
        VideoQuality(
          quality: 'Audio',
          format: 'mp3',
          downloadUrl: '',
          hasAudio: true,
          hasVideo: false,
        ),
      ];

      return VideoInfo(
        id: videoId,
        title: title,
        url: originalUrl,
        thumbnailUrl: thumbnailUrl,
        platform: 'Video Platform 1',
        duration: Duration(seconds: 0),
        availableQualities: qualities,
      );
    } catch (e) {
      print('Error parsing YouTube HTML: $e');
    }
    return null;
  }

  static VideoInfo? _parseTikTokHtml(String html, String originalUrl) {
    // Similar HTML parsing for TikTok
    return null;
  }

  static VideoInfo? _parseInstagramHtml(String html, String originalUrl) {
    // Similar HTML parsing for Instagram
    return null;
  }

  // Y2mate response parser
  static VideoInfo? _parseY2mateResponse(
    Map<String, dynamic> data,
    String originalUrl,
    String videoId,
  ) {
    try {
      if (data['status'] == 'ok' && data['mess'] != null) {
        final qualities = <VideoQuality>[];
        final mess = data['mess'];

        // Parse video qualities from HTML response
        final videoRegex = RegExp(
          r'data-fquality="(\d+p?)".*?data-ftype="(mp4|webm)".*?k="([^"]+)"',
        );
        final audioRegex = RegExp(
          r'data-fquality="(\d+kbps?)".*?data-ftype="(mp3|m4a)".*?k="([^"]+)"',
        );

        final videoMatches = videoRegex.allMatches(mess);
        for (final match in videoMatches) {
          final quality = match.group(1) ?? '';
          final format = match.group(2) ?? 'mp4';
          final k = match.group(3) ?? '';

          qualities.add(
            VideoQuality(
              quality: quality,
              format: format,
              downloadUrl:
                  'https://www.y2mate.com/mates/en68/ajax/convert?k=$k',
              hasAudio: true,
              hasVideo: true,
            ),
          );
        }

        final audioMatches = audioRegex.allMatches(mess);
        for (final match in audioMatches) {
          final quality = match.group(1) ?? '';
          final format = match.group(2) ?? 'mp3';
          final k = match.group(3) ?? '';

          qualities.add(
            VideoQuality(
              quality: 'Audio $quality',
              format: format,
              downloadUrl:
                  'https://www.y2mate.com/mates/en68/ajax/convert?k=$k',
              hasAudio: true,
              hasVideo: false,
            ),
          );
        }

        // Extract title
        final titleMatch = RegExp(r'<b>([^<]+)</b>').firstMatch(mess);
        final title = titleMatch?.group(1) ?? 'YouTube Video';

        return VideoInfo(
          id: videoId,
          title: title,
          url: originalUrl,
          thumbnailUrl: 'https://img.youtube.com/vi/$videoId/hqdefault.jpg',
          platform: 'Video Platform 1',
          duration: Duration(seconds: 0),
          availableQualities: qualities,
        );
      }
    } catch (e) {
      // Continue to next method
    }
    return null;
  }

  // YT1S response parser
  static VideoInfo? _parseYT1SResponse(
    Map<String, dynamic> data,
    String originalUrl,
    String videoId,
  ) {
    try {
      if (data['status'] == 'ok' && data['mess'] != null) {
        final qualities = <VideoQuality>[];
        final mess = data['mess'];

        // Parse video qualities from HTML response
        final videoRegex = RegExp(
          r'data-fquality="(\d+p?)".*?data-ftype="(mp4|webm)".*?data-k="([^"]+)"',
        );
        final audioRegex = RegExp(
          r'data-fquality="(\d+kbps?)".*?data-ftype="(mp3|m4a)".*?data-k="([^"]+)"',
        );

        final videoMatches = videoRegex.allMatches(mess);
        for (final match in videoMatches) {
          final quality = match.group(1) ?? '';
          final format = match.group(2) ?? 'mp4';
          final k = match.group(3) ?? '';

          qualities.add(
            VideoQuality(
              quality: quality,
              format: format,
              downloadUrl: 'https://yt1s.com/api/ajaxConvert/convert?k=$k',
              hasAudio: true,
              hasVideo: true,
            ),
          );
        }

        final audioMatches = audioRegex.allMatches(mess);
        for (final match in audioMatches) {
          final quality = match.group(1) ?? '';
          final format = match.group(2) ?? 'mp3';
          final k = match.group(3) ?? '';

          qualities.add(
            VideoQuality(
              quality: 'Audio $quality',
              format: format,
              downloadUrl: 'https://yt1s.com/api/ajaxConvert/convert?k=$k',
              hasAudio: true,
              hasVideo: false,
            ),
          );
        }

        // Extract title
        final titleMatch = RegExp(r'<b>([^<]+)</b>').firstMatch(mess);
        final title = titleMatch?.group(1) ?? 'YouTube Video';

        return VideoInfo(
          id: videoId,
          title: title,
          url: originalUrl,
          thumbnailUrl: 'https://img.youtube.com/vi/$videoId/hqdefault.jpg',
          platform: 'Video Platform 1',
          duration: Duration(seconds: 0),
          availableQualities: qualities,
        );
      }
    } catch (e) {
      // Continue to next method
    }
    return null;
  }

  // SnapSave response parser
  static VideoInfo? _parseSnapSaveResponse(
    Map<String, dynamic> data,
    String originalUrl,
  ) {
    try {
      if (data['status'] == 'ok' && data['data'] != null) {
        final videoData = data['data'];
        final qualities = <VideoQuality>[];

        // Parse video qualities
        if (videoData['video'] != null) {
          final videos = videoData['video'] as List;
          for (final video in videos) {
            qualities.add(
              VideoQuality(
                quality: video['quality'] ?? 'HD',
                format: 'mp4',
                downloadUrl: video['url'] ?? '',
                hasAudio: true,
                hasVideo: true,
              ),
            );
          }
        }

        // Parse audio if available
        if (videoData['audio'] != null) {
          qualities.add(
            VideoQuality(
              quality: 'Audio',
              format: 'mp3',
              downloadUrl: videoData['audio'],
              hasAudio: true,
              hasVideo: false,
            ),
          );
        }

        return VideoInfo(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: videoData['title'] ?? 'Instagram Video',
          url: originalUrl,
          thumbnailUrl: videoData['thumbnail'] ?? '',
          platform: 'Photo & Video Platform',
          duration: Duration(seconds: 0),
          availableQualities: qualities,
          uploader: videoData['username'],
        );
      }
    } catch (e) {
      // Continue to next method
    }
    return null;
  }
}
