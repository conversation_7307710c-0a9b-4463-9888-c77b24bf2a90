import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/download_item.dart';
import '../models/video_info.dart';
import '../models/video_quality.dart';

/// Local download service that creates demo files for testing
class LocalDownloadService {
  static final Dio _dio = Dio();

  /// Start download with demo content
  static Future<void> startDownload(
    DownloadItem item,
    Function(double) onProgress,
    Function(String) onComplete,
    Function(String) onError,
  ) async {
    try {
      print('Starting local download for: ${item.title}');
      
      // Get download directory
      final directory = await getApplicationDocumentsDirectory();
      final downloadsDir = Directory(path.join(directory.path, 'Downloads'));
      if (!await downloadsDir.exists()) {
        await downloadsDir.create(recursive: true);
      }

      // Generate filename
      final filename = _generateFilename(item.title, item.format);
      final filePath = path.join(downloadsDir.path, filename);

      // Check if it's a real URL or demo
      if (await _isRealDownloadUrl(item.downloadUrl)) {
        await _downloadRealFile(item, filePath, onProgress, onComplete, onError);
      } else {
        await _createDemoFile(item, filePath, onProgress, onComplete, onError);
      }
    } catch (e) {
      print('Download error: $e');
      onError('خطأ في التحميل: ${e.toString()}');
    }
  }

  /// Check if URL is a real download URL
  static Future<bool> _isRealDownloadUrl(String url) async {
    try {
      // Check if URL points to a direct file
      if (url.contains('.mp4') || url.contains('.mp3') || url.contains('.webm')) {
        final response = await _dio.head(url);
        return response.statusCode == 200;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Download real file
  static Future<void> _downloadRealFile(
    DownloadItem item,
    String filePath,
    Function(double) onProgress,
    Function(String) onComplete,
    Function(String) onError,
  ) async {
    try {
      await _dio.download(
        item.downloadUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = received / total;
            onProgress(progress);
          }
        },
        options: Options(
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
        ),
      );

      print('Real download completed: $filePath');
      onComplete(filePath);
    } catch (e) {
      print('Real download failed: $e');
      // Fallback to demo file
      await _createDemoFile(item, filePath, onProgress, onComplete, onError);
    }
  }

  /// Create demo file for testing
  static Future<void> _createDemoFile(
    DownloadItem item,
    String filePath,
    Function(double) onProgress,
    Function(String) onComplete,
    Function(String) onError,
  ) async {
    try {
      print('Creating demo file for: ${item.title}');
      
      // Simulate download progress
      final totalSize = _getDemoFileSize(item.format);
      final chunkSize = totalSize ~/ 20; // 20 chunks for smooth progress
      
      final file = File(filePath);
      final sink = file.openWrite();
      
      for (int i = 0; i < 20; i++) {
        // Simulate network delay
        await Future.delayed(const Duration(milliseconds: 200));
        
        // Write demo data
        final chunk = _generateDemoChunk(chunkSize, item.format);
        sink.add(chunk);
        
        // Update progress
        final progress = (i + 1) / 20;
        onProgress(progress);
        
        print('Demo download progress: ${(progress * 100).toStringAsFixed(1)}%');
      }
      
      await sink.close();
      
      // Add metadata to file
      await _addMetadataToFile(filePath, item);
      
      print('Demo download completed: $filePath');
      onComplete(filePath);
    } catch (e) {
      print('Demo download failed: $e');
      onError('فشل في إنشاء الملف التجريبي: ${e.toString()}');
    }
  }

  /// Generate demo file chunk
  static Uint8List _generateDemoChunk(int size, String format) {
    final chunk = Uint8List(size);
    
    if (format.toLowerCase() == 'mp4') {
      // MP4 header-like data
      final header = [0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70]; // ftyp
      for (int i = 0; i < header.length && i < size; i++) {
        chunk[i] = header[i];
      }
    } else if (format.toLowerCase() == 'mp3') {
      // MP3 header-like data
      final header = [0xFF, 0xFB, 0x90, 0x00]; // MP3 sync
      for (int i = 0; i < header.length && i < size; i++) {
        chunk[i] = header[i];
      }
    }
    
    // Fill rest with demo data
    for (int i = 8; i < size; i++) {
      chunk[i] = (i % 256);
    }
    
    return chunk;
  }

  /// Get demo file size based on format
  static int _getDemoFileSize(String format) {
    switch (format.toLowerCase()) {
      case 'mp4':
        return 5 * 1024 * 1024; // 5MB for video
      case 'mp3':
        return 3 * 1024 * 1024; // 3MB for audio
      default:
        return 4 * 1024 * 1024; // 4MB default
    }
  }

  /// Add metadata to downloaded file
  static Future<void> _addMetadataToFile(String filePath, DownloadItem item) async {
    try {
      // Create a metadata file alongside the video
      final metadataPath = '${filePath}.info';
      final metadata = {
        'title': item.title,
        'platform': item.platform,
        'url': item.url,
        'quality': item.quality,
        'format': item.format,
        'download_date': DateTime.now().toIso8601String(),
      };
      
      final metadataFile = File(metadataPath);
      await metadataFile.writeAsString(metadata.toString());
    } catch (e) {
      print('Failed to write metadata: $e');
    }
  }

  /// Generate safe filename
  static String _generateFilename(String title, String format) {
    // Clean title for filename
    String cleanTitle = title
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '') // Remove invalid chars
        .replaceAll(RegExp(r'\s+'), '_') // Replace spaces with underscores
        .trim();
    
    // Limit length
    if (cleanTitle.length > 50) {
      cleanTitle = cleanTitle.substring(0, 50);
    }
    
    // Add timestamp to avoid conflicts
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    
    return '${cleanTitle}_$timestamp.$format';
  }

  /// Get downloads directory
  static Future<Directory> getDownloadsDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    final downloadsDir = Directory(path.join(directory.path, 'Downloads'));
    if (!await downloadsDir.exists()) {
      await downloadsDir.create(recursive: true);
    }
    return downloadsDir;
  }

  /// List downloaded files
  static Future<List<FileSystemEntity>> listDownloadedFiles() async {
    try {
      final downloadsDir = await getDownloadsDirectory();
      return downloadsDir.listSync();
    } catch (e) {
      print('Failed to list downloads: $e');
      return [];
    }
  }

  /// Delete downloaded file
  static Future<bool> deleteDownloadedFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        
        // Also delete metadata file if exists
        final metadataFile = File('$filePath.info');
        if (await metadataFile.exists()) {
          await metadataFile.delete();
        }
        
        return true;
      }
      return false;
    } catch (e) {
      print('Failed to delete file: $e');
      return false;
    }
  }
}
