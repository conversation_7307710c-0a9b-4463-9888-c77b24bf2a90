{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\AndroidStudioProjects\\Download_videos\\android\\app\\.cxx\\Debug\\5012114y\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\AndroidStudioProjects\\Download_videos\\android\\app\\.cxx\\Debug\\5012114y\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "F:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "F:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}