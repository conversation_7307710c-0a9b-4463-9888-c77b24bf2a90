import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

import '../../domain/entities/download_item.dart';

part 'download_item_model.freezed.dart';
part 'download_item_model.g.dart';

@freezed
@HiveType(typeId: 2)
class DownloadItemModel with _$DownloadItemModel {
  const factory DownloadItemModel({
    @HiveField(0) required String id,
    @HiveField(1) required String videoId,
    @HiveField(2) required String title,
    @HiveField(3) required String url,
    @HiveField(4) required String downloadUrl,
    @HiveField(5) required String thumbnailUrl,
    @HiveField(6) required String platform,
    @HiveField(7) required String quality,
    @HiveField(8) required String format,
    @HiveField(9) required String filePath,
    @HiveField(10) required String status,
    @HiveField(11) required double progress,
    @HiveField(12) required DateTime createdAt,
    @HiveField(13) DateTime? startedAt,
    @HiveField(14) DateTime? completedAt,
    @HiveField(15) int? totalBytes,
    @HiveField(16) int? downloadedBytes,
    @HiveField(17) String? errorMessage,
    @HiveField(18) int? retryCount,
    @HiveField(19) double? downloadSpeed,
    @HiveField(20) Duration? estimatedTimeRemaining,
  }) = _DownloadItemModel;

  factory DownloadItemModel.fromJson(Map<String, dynamic> json) =>
      _$DownloadItemModelFromJson(json);
}

/// Extension to convert model to entity
extension DownloadItemModelX on DownloadItemModel {
  DownloadItem toEntity() {
    return DownloadItem(
      id: id,
      videoId: videoId,
      title: title,
      url: url,
      downloadUrl: downloadUrl,
      thumbnailUrl: thumbnailUrl,
      platform: platform,
      quality: quality,
      format: format,
      filePath: filePath,
      status: DownloadStatus.values.firstWhere(
        (s) => s.name == status,
        orElse: () => DownloadStatus.pending,
      ),
      progress: progress,
      createdAt: createdAt,
      startedAt: startedAt,
      completedAt: completedAt,
      totalBytes: totalBytes,
      downloadedBytes: downloadedBytes,
      errorMessage: errorMessage,
      retryCount: retryCount ?? 0,
      downloadSpeed: downloadSpeed,
      estimatedTimeRemaining: estimatedTimeRemaining,
    );
  }
}

/// Extension to convert entity to model
extension DownloadItemX on DownloadItem {
  DownloadItemModel toModel() {
    return DownloadItemModel(
      id: id,
      videoId: videoId,
      title: title,
      url: url,
      downloadUrl: downloadUrl,
      thumbnailUrl: thumbnailUrl,
      platform: platform,
      quality: quality,
      format: format,
      filePath: filePath,
      status: status.name,
      progress: progress,
      createdAt: createdAt,
      startedAt: startedAt,
      completedAt: completedAt,
      totalBytes: totalBytes,
      downloadedBytes: downloadedBytes,
      errorMessage: errorMessage,
      retryCount: retryCount,
      downloadSpeed: downloadSpeed,
      estimatedTimeRemaining: estimatedTimeRemaining,
    );
  }
}
