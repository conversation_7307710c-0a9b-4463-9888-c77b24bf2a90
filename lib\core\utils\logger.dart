import 'package:logger/logger.dart';
import 'package:injectable/injectable.dart';

/// Application logger service
@singleton
class AppLogger {
  late final Logger _logger;

  AppLogger() {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
      level: Level.debug,
    );
  }

  /// Log debug message
  void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Log info message
  void info(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning message
  void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log error message
  void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log fatal error message
  void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Log network request
  void logRequest(String method, String url, Map<String, dynamic>? data) {
    _logger.d('🌐 $method $url', error: data);
  }

  /// Log network response
  void logResponse(String method, String url, int statusCode, dynamic data) {
    _logger.d('📡 $method $url - $statusCode', error: data);
  }

  /// Log download progress
  void logDownloadProgress(String fileName, double progress) {
    _logger.d('📥 Downloading $fileName: ${(progress * 100).toStringAsFixed(1)}%');
  }

  /// Log video extraction
  void logVideoExtraction(String platform, String url, bool success) {
    final emoji = success ? '✅' : '❌';
    _logger.d('$emoji Video extraction from $platform: $url');
  }
}
