import '../models/video_info.dart';
import '../models/video_quality.dart';
import 'platform_detector.dart';
import '../utils/download_fix_helper.dart';

/// Service to validate and enhance video information
class VideoInfoValidator {
  /// Validates and enhances video information
  static VideoInfo? validateAndEnhance(VideoInfo? videoInfo) {
    if (videoInfo == null) return null;

    // Ensure we have valid download URLs
    final validQualities =
        videoInfo.availableQualities
            .where((quality) => _isValidDownloadUrl(quality.downloadUrl))
            .toList();

    // If no valid qualities, try to generate fallback URLs
    if (validQualities.isEmpty) {
      final fallbackQualities = _generateFallbackQualities(videoInfo);
      if (fallbackQualities.isNotEmpty) {
        return videoInfo.copyWith(availableQualities: fallbackQualities);
      }
      return null; // No valid download options
    }

    // Enhance video info with better metadata
    return videoInfo.copyWith(
      title: _enhanceTitle(videoInfo.title, videoInfo.platform),
      thumbnailUrl: _enhanceThumbnailUrl(
        videoInfo.thumbnailUrl,
        videoInfo.url,
        videoInfo.platform,
      ),
      duration: videoInfo.duration ?? const Duration(seconds: 180),
      availableQualities: validQualities,
    );
  }

  /// Checks if a download URL is valid
  static bool _isValidDownloadUrl(String url) {
    if (url.isEmpty) return false;
    if (!url.startsWith('http')) return false;
    if (url.contains('placeholder')) return false;
    return true;
  }

  /// Generates fallback quality options
  static List<VideoQuality> _generateFallbackQualities(VideoInfo videoInfo) {
    final platform = PlatformDetector.detectPlatform(videoInfo.url);

    // For now, return empty list - in production, you'd implement
    // direct extraction methods here
    return [];
  }

  /// Enhances video title
  static String _enhanceTitle(String title, String platform) {
    if (title.isEmpty || title == 'Downloaded Video' || title == 'Unknown') {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return '$platform Video $timestamp';
    }

    // Clean up title
    String cleanTitle =
        title
            .replaceAll(
              RegExp(r'[<>:"/\\|?*]'),
              '',
            ) // Remove invalid filename chars
            .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
            .trim();

    if (cleanTitle.length > 100) {
      cleanTitle = '${cleanTitle.substring(0, 97)}...';
    }

    return cleanTitle.isNotEmpty ? cleanTitle : '$platform Video';
  }

  /// Enhances thumbnail URL
  static String _enhanceThumbnailUrl(
    String thumbnailUrl,
    String videoUrl,
    String platform,
  ) {
    if (thumbnailUrl.isNotEmpty && !thumbnailUrl.contains('placeholder')) {
      return thumbnailUrl;
    }

    // Generate platform-specific thumbnail
    final platformType = PlatformDetector.detectPlatform(videoUrl);
    final videoId = PlatformDetector.extractVideoId(videoUrl, platformType);

    switch (platformType) {
      case SupportedPlatform.youtube:
        if (videoId != null) {
          return 'https://img.youtube.com/vi/$videoId/hqdefault.jpg';
        }
        break;
      case SupportedPlatform.tiktok:
        return 'https://via.placeholder.com/480x640/FF0050/FFFFFF?text=TikTok';
      case SupportedPlatform.instagram:
        return 'https://via.placeholder.com/480x480/E4405F/FFFFFF?text=Instagram';
      case SupportedPlatform.facebook:
        return 'https://via.placeholder.com/480x360/1877F2/FFFFFF?text=Facebook';
      case SupportedPlatform.twitter:
        return 'https://via.placeholder.com/480x360/1DA1F2/FFFFFF?text=Twitter';
      default:
        return 'https://via.placeholder.com/480x360/666666/FFFFFF?text=Video';
    }

    return 'https://via.placeholder.com/480x360/666666/FFFFFF?text=Video';
  }

  /// Validates that video info has minimum required data
  static bool hasMinimumRequiredData(VideoInfo videoInfo) {
    return videoInfo.id.isNotEmpty &&
        videoInfo.title.isNotEmpty &&
        videoInfo.url.isNotEmpty &&
        videoInfo.availableQualities.isNotEmpty &&
        videoInfo.availableQualities.any(
          (q) => _isValidDownloadUrl(q.downloadUrl),
        );
  }
}
