import 'package:equatable/equatable.dart';

/// Video quality entity
class VideoQuality extends Equatable {
  const VideoQuality({
    required this.quality,
    required this.format,
    required this.downloadUrl,
    this.fileSize,
    required this.hasAudio,
    required this.hasVideo,
    this.codec,
    this.bitrate,
    this.resolution,
    this.fps,
    this.isHdr = false,
  });

  final String quality;
  final String format;
  final String downloadUrl;
  final int? fileSize;
  final bool hasAudio;
  final bool hasVideo;
  final String? codec;
  final int? bitrate;
  final String? resolution;
  final double? fps;
  final bool isHdr;

  /// Get display name for the quality
  String get displayName {
    final buffer = StringBuffer();
    
    if (hasVideo && hasAudio) {
      buffer.write('$quality ($format)');
      if (isHdr) buffer.write(' HDR');
    } else if (hasVideo) {
      buffer.write('$quality Video Only ($format)');
      if (isHdr) buffer.write(' HDR');
    } else if (hasAudio) {
      buffer.write('Audio Only');
      if (bitrate != null) {
        buffer.write(' ${bitrate}kbps');
      }
      buffer.write(' ($format)');
    } else {
      buffer.write(quality);
    }
    
    return buffer.toString();
  }

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize == null) return 'Unknown size';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    double size = fileSize!.toDouble();
    int unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }

  /// Get quality priority for sorting
  int get qualityPriority {
    final qualityMap = {
      '2160p': 8,
      '1440p': 7,
      '1080p': 6,
      '720p': 5,
      '480p': 4,
      '360p': 3,
      '240p': 2,
      '144p': 1,
    };
    
    return qualityMap[quality] ?? 0;
  }

  /// Check if this is a high quality option
  bool get isHighQuality {
    return qualityPriority >= 5; // 720p and above
  }

  /// Check if this is an audio-only option
  bool get isAudioOnly => hasAudio && !hasVideo;

  /// Check if this is a video-only option
  bool get isVideoOnly => hasVideo && !hasAudio;

  /// Check if this is a combined audio+video option
  bool get isCombined => hasAudio && hasVideo;

  /// Get estimated download time based on file size and connection speed
  Duration? getEstimatedDownloadTime(double speedMbps) {
    if (fileSize == null) return null;
    
    final fileSizeMb = fileSize! / (1024 * 1024);
    final timeSeconds = fileSizeMb / speedMbps;
    
    return Duration(seconds: timeSeconds.round());
  }

  @override
  List<Object?> get props => [
        quality,
        format,
        downloadUrl,
        fileSize,
        hasAudio,
        hasVideo,
        codec,
        bitrate,
        resolution,
        fps,
        isHdr,
      ];

  @override
  String toString() => 'VideoQuality(quality: $quality, format: $format, hasAudio: $hasAudio, hasVideo: $hasVideo)';
}
