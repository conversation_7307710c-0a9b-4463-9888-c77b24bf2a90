                        -HC:\src\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=F:\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=F:\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=F:\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=F:\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\AndroidStudioProjects\Download_videos\build\app\intermediates\cxx\Debug\5012114y\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\AndroidStudioProjects\Download_videos\build\app\intermediates\cxx\Debug\5012114y\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BF:\AndroidStudioProjects\Download_videos\android\app\.cxx\Debug\5012114y\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2