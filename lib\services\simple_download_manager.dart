import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/download_item.dart';
import '../models/video_info.dart';
import '../models/video_quality.dart';
import 'database_service.dart';
import 'notification_service.dart';
import 'local_download_service.dart';

/// Simplified download manager using local download service
class SimpleDownloadManager {
  static final SimpleDownloadManager _instance =
      SimpleDownloadManager._internal();
  factory SimpleDownloadManager() => _instance;
  SimpleDownloadManager._internal();

  final DatabaseService _db = DatabaseService();
  final NotificationService _notifications = NotificationService();

  final Map<String, DownloadItem> _activeDownloads = {};
  final Map<String, Function(DownloadItem)> _progressCallbacks = {};

  /// Start a new download
  Future<String> startDownload({
    required VideoInfo videoInfo,
    required VideoQuality selectedQuality,
    Function(DownloadItem)? onProgress,
  }) async {
    final downloadId = DateTime.now().millisecondsSinceEpoch.toString();

    // Get download directory
    final directory = await _getDownloadDirectory();
    final filename = _generateFileName(videoInfo.title, selectedQuality.format);
    final filePath = path.join(directory.path, filename);

    // Create download item
    final downloadItem = DownloadItem(
      id: downloadId,
      videoId: videoInfo.id,
      title: videoInfo.title,
      url: videoInfo.url,
      downloadUrl: selectedQuality.downloadUrl,
      thumbnailUrl: videoInfo.thumbnailUrl,
      filePath: filePath,
      platform: videoInfo.platform,
      quality: selectedQuality.quality,
      format: selectedQuality.format,
      status: DownloadStatus.pending,
      createdAt: DateTime.now(),
      progress: 0.0,
    );

    // Save to database
    await _db.insertDownload(downloadItem);

    // Set progress callback
    if (onProgress != null) {
      _progressCallbacks[downloadId] = onProgress;
    }

    // Start download
    _performDownload(downloadItem);

    return downloadId;
  }

  /// Perform the actual download
  Future<void> _performDownload(DownloadItem item) async {
    _activeDownloads[item.id] = item;

    try {
      print('Starting simple download for: ${item.title}');

      // Update status to downloading
      final updatedItem = item.copyWith(status: DownloadStatus.downloading);
      await _updateDownloadItem(updatedItem);

      // Show notification
      await _notifications.showDownloadStarted(item.title);

      // Use local download service
      await LocalDownloadService.startDownload(
        item,
        (progress) async {
          final progressItem = _activeDownloads[item.id]!.copyWith(
            progress: progress,
            downloadedBytes:
                (progress * 5 * 1024 * 1024).toInt(), // Estimate 5MB
            totalBytes: 5 * 1024 * 1024, // 5MB total
            status: DownloadStatus.downloading,
          );
          await _updateDownloadItem(progressItem);

          // Update notification
          await _notifications.updateDownloadProgress(
            item.title,
            (progress * 100).toInt(),
          );

          // Log progress
          print('Download progress: ${(progress * 100).toStringAsFixed(1)}%');
        },
        (filePath) async {
          // Download completed successfully
          print('Download completed: ${item.title}');

          final completedItem = _activeDownloads[item.id]!.copyWith(
            status: DownloadStatus.completed,
            completedAt: DateTime.now(),
            progress: 1.0,
            filePath: filePath,
          );

          await _updateDownloadItem(completedItem);
          await _notifications.showDownloadCompleted(item.title, filePath);
        },
        (error) async {
          // Download failed
          print('Download failed: ${item.title} - $error');

          final failedItem = _activeDownloads[item.id]!.copyWith(
            status: DownloadStatus.failed,
            errorMessage: error,
          );

          await _updateDownloadItem(failedItem);
          await _notifications.showDownloadFailed(item.title);
        },
      );
    } catch (e) {
      print('Download error: $e');

      final failedItem = _activeDownloads[item.id]!.copyWith(
        status: DownloadStatus.failed,
        errorMessage: e.toString(),
      );

      await _updateDownloadItem(failedItem);
      await _notifications.showDownloadFailed(item.title);
    } finally {
      _activeDownloads.remove(item.id);
      _progressCallbacks.remove(item.id);
    }
  }

  /// Update download item in database and notify callbacks
  Future<void> _updateDownloadItem(DownloadItem item) async {
    _activeDownloads[item.id] = item;
    await _db.updateDownload(item);

    // Notify progress callback
    final callback = _progressCallbacks[item.id];
    if (callback != null) {
      callback(item);
    }
  }

  /// Get download directory
  Future<Directory> _getDownloadDirectory() async {
    Directory? directory;

    if (Platform.isAndroid) {
      try {
        // Try to get external storage directory first
        directory = await getExternalStorageDirectory();
        if (directory != null) {
          // Navigate to the public Downloads folder
          final List<String> pathSegments = directory.path.split('/');
          final int androidIndex = pathSegments.indexOf('Android');
          if (androidIndex != -1) {
            final String publicPath = pathSegments
                .sublist(0, androidIndex)
                .join('/');
            directory = Directory(
              path.join(publicPath, 'Download', 'VideoDownloader'),
            );
          } else {
            directory = Directory(path.join(directory.path, 'VideoDownloads'));
          }
        }
      } catch (e) {
        print('Failed to get external storage directory: $e');
        // Fallback to app documents directory
        directory = await getApplicationDocumentsDirectory();
        directory = Directory(path.join(directory.path, 'Downloads'));
      }
    }

    // Fallback to app documents directory
    directory ??= await getApplicationDocumentsDirectory();
    directory = Directory(path.join(directory.path, 'Downloads'));

    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    print('Download directory: ${directory.path}');
    return directory;
  }

  /// Generate safe filename
  String _generateFileName(String title, String format) {
    // Clean title for filename
    String cleanTitle =
        title
            .replaceAll(RegExp(r'[<>:"/\\|?*]'), '')
            .replaceAll(RegExp(r'\s+'), '_')
            .trim();

    if (cleanTitle.length > 50) {
      cleanTitle = cleanTitle.substring(0, 50);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${cleanTitle}_$timestamp.$format';
  }

  /// Get all downloads
  Future<List<DownloadItem>> getAllDownloads() async {
    return await _db.getAllDownloads();
  }

  /// Get downloads by status
  Future<List<DownloadItem>> getDownloadsByStatus(DownloadStatus status) async {
    return await _db.getDownloadsByStatus(status);
  }

  /// Get specific download
  Future<DownloadItem?> getDownload(String downloadId) async {
    return await _db.getDownload(downloadId);
  }

  /// Delete download
  Future<void> deleteDownload(String downloadId) async {
    final item = await _db.getDownload(downloadId);
    if (item != null) {
      // Delete file
      final file = File(item.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      // Remove from database
      await _db.deleteDownload(downloadId);
    }
  }

  /// Check if download is active
  bool isDownloading(String downloadId) {
    return _activeDownloads.containsKey(downloadId);
  }

  /// Get download progress
  double getDownloadProgress(String downloadId) {
    final item = _activeDownloads[downloadId];
    return item?.progress ?? 0.0;
  }

  /// Clear completed downloads
  Future<void> clearCompletedDownloads() async {
    final completed = await getDownloadsByStatus(DownloadStatus.completed);
    for (final item in completed) {
      await _db.deleteDownload(item.id);
    }
  }

  void dispose() {
    _activeDownloads.clear();
    _progressCallbacks.clear();
  }
}
