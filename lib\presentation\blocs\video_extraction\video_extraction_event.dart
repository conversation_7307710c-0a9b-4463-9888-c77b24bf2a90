part of 'video_extraction_bloc.dart';

/// Base class for video extraction events
abstract class VideoExtractionEvent extends Equatable {
  const VideoExtractionEvent();

  @override
  List<Object?> get props => [];
}

/// Event to start video extraction
class VideoExtractionStarted extends VideoExtractionEvent {
  const VideoExtractionStarted({
    required this.url,
    this.useCache = true,
    this.cacheResult = true,
  });

  final String url;
  final bool useCache;
  final bool cacheResult;

  @override
  List<Object?> get props => [url, useCache, cacheResult];
}

/// Event to clear video extraction state
class VideoExtractionCleared extends VideoExtractionEvent {
  const VideoExtractionCleared();
}
