import 'package:equatable/equatable.dart';

import 'video_quality.dart';

/// Video information entity
class VideoInfo extends Equatable {
  const VideoInfo({
    required this.id,
    required this.title,
    required this.url,
    required this.thumbnailUrl,
    required this.platform,
    required this.duration,
    required this.availableQualities,
    this.description,
    this.uploader,
    this.uploadDate,
    this.viewCount,
    this.likeCount,
    this.tags,
    this.hasWatermark = false,
    this.extractedAt,
  });

  final String id;
  final String title;
  final String url;
  final String thumbnailUrl;
  final String platform;
  final Duration duration;
  final List<VideoQuality> availableQualities;
  final String? description;
  final String? uploader;
  final DateTime? uploadDate;
  final int? viewCount;
  final int? likeCount;
  final List<String>? tags;
  final bool hasWatermark;
  final DateTime? extractedAt;

  /// Get formatted duration string
  String get formattedDuration {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
          '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
          '${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Get formatted view count
  String get formattedViewCount {
    if (viewCount == null) return 'N/A';
    
    if (viewCount! >= 1000000) {
      return '${(viewCount! / 1000000).toStringAsFixed(1)}M views';
    } else if (viewCount! >= 1000) {
      return '${(viewCount! / 1000).toStringAsFixed(1)}K views';
    } else {
      return '$viewCount views';
    }
  }

  /// Get best quality available
  VideoQuality? get bestQuality {
    if (availableQualities.isEmpty) return null;
    
    // Sort by quality preference
    final sortedQualities = List<VideoQuality>.from(availableQualities);
    sortedQualities.sort((a, b) {
      final aQuality = _getQualityValue(a.quality);
      final bQuality = _getQualityValue(b.quality);
      return bQuality.compareTo(aQuality);
    });
    
    return sortedQualities.first;
  }

  /// Get audio-only qualities
  List<VideoQuality> get audioQualities {
    return availableQualities.where((q) => q.hasAudio && !q.hasVideo).toList();
  }

  /// Get video qualities
  List<VideoQuality> get videoQualities {
    return availableQualities.where((q) => q.hasVideo).toList();
  }

  /// Check if video has multiple qualities
  bool get hasMultipleQualities => availableQualities.length > 1;

  /// Check if video supports audio extraction
  bool get supportsAudioExtraction => audioQualities.isNotEmpty;

  int _getQualityValue(String quality) {
    final qualityMap = {
      '2160p': 2160,
      '1440p': 1440,
      '1080p': 1080,
      '720p': 720,
      '480p': 480,
      '360p': 360,
      '240p': 240,
      '144p': 144,
    };
    
    return qualityMap[quality] ?? 0;
  }

  @override
  List<Object?> get props => [
        id,
        title,
        url,
        thumbnailUrl,
        platform,
        duration,
        availableQualities,
        description,
        uploader,
        uploadDate,
        viewCount,
        likeCount,
        tags,
        hasWatermark,
        extractedAt,
      ];

  @override
  String toString() => 'VideoInfo(id: $id, title: $title, platform: $platform)';
}
