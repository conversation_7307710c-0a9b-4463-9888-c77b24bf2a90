{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\AndroidStudioProjects\\Download_videos\\android\\app\\.cxx\\Debug\\5012114y\\x86_64", "clean"]], "buildTargetsCommandComponents": ["F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\AndroidStudioProjects\\Download_videos\\android\\app\\.cxx\\Debug\\5012114y\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}