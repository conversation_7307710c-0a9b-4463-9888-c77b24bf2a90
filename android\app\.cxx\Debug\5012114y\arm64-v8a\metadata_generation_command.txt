                        -HC:\src\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=F:\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=F:\Android\Sdk\ndk\27.0.12077973
-<PERSON>MAKE_TOOLCHAIN_FILE=F:\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=F:\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\AndroidStudioProjects\Download_videos\build\app\intermediates\cxx\Debug\5012114y\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\AndroidStudioProjects\Download_videos\build\app\intermediates\cxx\Debug\5012114y\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BF:\AndroidStudioProjects\Download_videos\android\app\.cxx\Debug\5012114y\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2