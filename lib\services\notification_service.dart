import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();
  bool _initialized = false;

  Future<void> initialize() async {
    if (_initialized) return;

    // Request notification permission
    await _requestNotificationPermission();

    // Initialize notifications
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    await _createNotificationChannels();

    _initialized = true;
  }

  Future<void> _requestNotificationPermission() async {
    final status = await Permission.notification.request();
    if (status.isDenied) {
      // Handle permission denied
      print('Notification permission denied');
    }
  }

  Future<void> _createNotificationChannels() async {
    // Download progress channel
    const downloadChannel = AndroidNotificationChannel(
      'download_progress',
      'Download Progress',
      description: 'Shows download progress for videos',
      importance: Importance.low,
      showBadge: false,
    );

    // Download completed channel
    const completedChannel = AndroidNotificationChannel(
      'download_completed',
      'Download Completed',
      description: 'Notifies when downloads are completed',
      importance: Importance.high,
    );

    // Download failed channel
    const failedChannel = AndroidNotificationChannel(
      'download_failed',
      'Download Failed',
      description: 'Notifies when downloads fail',
      importance: Importance.high,
    );

    await _notifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(downloadChannel);

    await _notifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(completedChannel);

    await _notifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(failedChannel);
  }

  Future<void> showDownloadStarted(String videoTitle) async {
    const androidDetails = AndroidNotificationDetails(
      'download_progress',
      'Download Progress',
      channelDescription: 'Shows download progress for videos',
      importance: Importance.low,
      priority: Priority.low,
      showProgress: true,
      maxProgress: 100,
      progress: 0,
      indeterminate: true,
      ongoing: true,
      autoCancel: false,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: false,
      presentBadge: false,
      presentSound: false,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      videoTitle.hashCode,
      'Downloading Video',
      videoTitle,
      details,
    );
  }

  Future<void> updateDownloadProgress(String videoTitle, int progress) async {
    const iosDetails = DarwinNotificationDetails(
      presentAlert: false,
      presentBadge: false,
      presentSound: false,
    );

    final details = NotificationDetails(
      android: AndroidNotificationDetails(
        'download_progress',
        'Download Progress',
        channelDescription: 'Shows download progress for videos',
        importance: Importance.low,
        priority: Priority.low,
        showProgress: true,
        maxProgress: 100,
        progress: progress,
        ongoing: true,
        autoCancel: false,
      ),
      iOS: iosDetails,
    );

    await _notifications.show(
      videoTitle.hashCode,
      'Downloading Video ($progress%)',
      videoTitle,
      details,
    );
  }

  Future<void> showDownloadCompleted(String videoTitle) async {
    // Cancel progress notification
    await _notifications.cancel(videoTitle.hashCode);

    const androidDetails = AndroidNotificationDetails(
      'download_completed',
      'Download Completed',
      channelDescription: 'Notifies when downloads are completed',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@drawable/ic_download_done',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      videoTitle.hashCode + 1000,
      'Download Completed',
      videoTitle,
      details,
    );
  }

  Future<void> showDownloadFailed(String videoTitle) async {
    // Cancel progress notification
    await _notifications.cancel(videoTitle.hashCode);

    const androidDetails = AndroidNotificationDetails(
      'download_failed',
      'Download Failed',
      channelDescription: 'Notifies when downloads fail',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@drawable/ic_download_error',
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      videoTitle.hashCode + 2000,
      'Download Failed',
      'Failed to download: $videoTitle',
      details,
    );
  }

  Future<void> showMultipleDownloadsCompleted(int count) async {
    const androidDetails = AndroidNotificationDetails(
      'download_completed',
      'Download Completed',
      channelDescription: 'Notifies when downloads are completed',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      999999,
      'Downloads Completed',
      '$count videos have been downloaded successfully',
      details,
    );
  }

  Future<void> cancelDownloadNotification(String videoTitle) async {
    await _notifications.cancel(videoTitle.hashCode);
  }

  Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap
    // You can navigate to specific screens based on the notification
    print('Notification tapped: ${response.payload}');
  }

  // Schedule notifications
  Future<void> scheduleDownloadReminder() async {
    const androidDetails = AndroidNotificationDetails(
      'reminders',
      'Reminders',
      channelDescription: 'Reminder notifications',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    // TODO: Implement scheduled notifications with proper timezone setup
    // This requires additional configuration with timezone package
  }

  DateTime _nextInstanceOfTime(int hour, int minute) {
    final now = DateTime.now();
    DateTime scheduledDate = DateTime(
      now.year,
      now.month,
      now.day,
      hour,
      minute,
    );

    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }

    return scheduledDate;
  }

  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  Future<void> cancelScheduledNotifications() async {
    await _notifications.cancelAll();
  }
}
