import '../../core/error/failures.dart';
import '../../core/utils/either.dart';
import '../entities/download_item.dart';
import '../entities/video_info.dart';
import '../entities/video_quality.dart';

/// Repository interface for download operations
abstract class DownloadRepository {
  /// Start a new download
  Future<Either<Failure, String>> startDownload({
    required VideoInfo videoInfo,
    required VideoQuality quality,
  });

  /// Pause a download
  Future<Either<Failure, void>> pauseDownload(String downloadId);

  /// Resume a download
  Future<Either<Failure, void>> resumeDownload(String downloadId);

  /// Cancel a download
  Future<Either<Failure, void>> cancelDownload(String downloadId);

  /// Retry a failed download
  Future<Either<Failure, void>> retryDownload(String downloadId);

  /// Delete a download
  Future<Either<Failure, void>> deleteDownload(String downloadId);

  /// Get all downloads
  Future<Either<Failure, List<DownloadItem>>> getAllDownloads();

  /// Get download by ID
  Future<Either<Failure, DownloadItem?>> getDownloadById(String downloadId);

  /// Get downloads by status
  Future<Either<Failure, List<DownloadItem>>> getDownloadsByStatus(
    DownloadStatus status,
  );

  /// Get active downloads
  Future<Either<Failure, List<DownloadItem>>> getActiveDownloads();

  /// Get completed downloads
  Future<Either<Failure, List<DownloadItem>>> getCompletedDownloads();

  /// Get failed downloads
  Future<Either<Failure, List<DownloadItem>>> getFailedDownloads();

  /// Clear completed downloads
  Future<Either<Failure, void>> clearCompletedDownloads();

  /// Clear failed downloads
  Future<Either<Failure, void>> clearFailedDownloads();

  /// Get download statistics
  Future<Either<Failure, Map<String, dynamic>>> getDownloadStatistics();

  /// Stream download progress
  Stream<DownloadItem> watchDownloadProgress(String downloadId);

  /// Stream all downloads
  Stream<List<DownloadItem>> watchAllDownloads();
}
