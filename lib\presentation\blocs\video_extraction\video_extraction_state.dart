part of 'video_extraction_bloc.dart';

/// Base class for video extraction states
abstract class <PERSON>ExtractionState extends Equatable {
  const VideoExtractionState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class VideoExtractionInitial extends VideoExtractionState {
  const VideoExtractionInitial();
}

/// Loading state
class VideoExtractionLoading extends VideoExtractionState {
  const VideoExtractionLoading();
}

/// Success state
class VideoExtractionSuccess extends VideoExtractionState {
  const VideoExtractionSuccess({required this.videoInfo});

  final VideoInfo videoInfo;

  @override
  List<Object?> get props => [videoInfo];
}

/// Failure state
class VideoExtractionFailure extends VideoExtractionState {
  const VideoExtractionFailure({
    required this.message,
    this.code,
  });

  final String message;
  final String? code;

  @override
  List<Object?> get props => [message, code];
}
