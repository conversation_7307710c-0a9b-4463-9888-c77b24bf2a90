import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/download_item.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;
  
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<void> initialize() async {
    await database;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'video_downloader.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    await db.execute('''
      CREATE TABLE downloads (
        id TEXT PRIMARY KEY,
        video_id TEXT NOT NULL,
        title TEXT NOT NULL,
        url TEXT NOT NULL,
        thumbnail_url TEXT NOT NULL,
        platform TEXT NOT NULL,
        quality TEXT NOT NULL,
        format TEXT NOT NULL,
        file_path TEXT NOT NULL,
        total_bytes INTEGER,
        downloaded_bytes INTEGER DEFAULT 0,
        status INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        completed_at TEXT,
        error_message TEXT,
        progress REAL DEFAULT 0.0
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_downloads_status ON downloads(status)');
    await db.execute('CREATE INDEX idx_downloads_platform ON downloads(platform)');
    await db.execute('CREATE INDEX idx_downloads_created_at ON downloads(created_at)');
  }

  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Example: Add new column in version 2
      // await db.execute('ALTER TABLE downloads ADD COLUMN new_column TEXT');
    }
  }

  // Download operations
  Future<void> insertDownload(DownloadItem item) async {
    final db = await database;
    await db.insert(
      'downloads',
      item.toJson(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<void> updateDownload(DownloadItem item) async {
    final db = await database;
    await db.update(
      'downloads',
      item.toJson(),
      where: 'id = ?',
      whereArgs: [item.id],
    );
  }

  Future<void> deleteDownload(String downloadId) async {
    final db = await database;
    await db.delete(
      'downloads',
      where: 'id = ?',
      whereArgs: [downloadId],
    );
  }

  Future<DownloadItem?> getDownload(String downloadId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'downloads',
      where: 'id = ?',
      whereArgs: [downloadId],
    );

    if (maps.isNotEmpty) {
      return DownloadItem.fromJson(maps.first);
    }
    return null;
  }

  Future<List<DownloadItem>> getAllDownloads() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'downloads',
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return DownloadItem.fromJson(maps[i]);
    });
  }

  Future<List<DownloadItem>> getDownloadsByStatus(DownloadStatus status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'downloads',
      where: 'status = ?',
      whereArgs: [status.index],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return DownloadItem.fromJson(maps[i]);
    });
  }

  Future<List<DownloadItem>> getDownloadsByPlatform(String platform) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'downloads',
      where: 'platform = ?',
      whereArgs: [platform],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return DownloadItem.fromJson(maps[i]);
    });
  }

  Future<List<DownloadItem>> searchDownloads(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'downloads',
      where: 'title LIKE ? OR platform LIKE ?',
      whereArgs: ['%$query%', '%$query%'],
      orderBy: 'created_at DESC',
    );

    return List.generate(maps.length, (i) {
      return DownloadItem.fromJson(maps[i]);
    });
  }

  // Statistics
  Future<Map<String, int>> getDownloadStats() async {
    final db = await database;
    
    final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM downloads');
    final total = totalResult.first['count'] as int;
    
    final completedResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM downloads WHERE status = ?',
      [DownloadStatus.completed.index],
    );
    final completed = completedResult.first['count'] as int;
    
    final failedResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM downloads WHERE status = ?',
      [DownloadStatus.failed.index],
    );
    final failed = failedResult.first['count'] as int;
    
    final downloadingResult = await db.rawQuery(
      'SELECT COUNT(*) as count FROM downloads WHERE status = ?',
      [DownloadStatus.downloading.index],
    );
    final downloading = downloadingResult.first['count'] as int;

    return {
      'total': total,
      'completed': completed,
      'failed': failed,
      'downloading': downloading,
    };
  }

  Future<Map<String, int>> getPlatformStats() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT platform, COUNT(*) as count 
      FROM downloads 
      GROUP BY platform 
      ORDER BY count DESC
    ''');

    final Map<String, int> stats = {};
    for (final map in maps) {
      stats[map['platform'] as String] = map['count'] as int;
    }
    
    return stats;
  }

  Future<int> getTotalDownloadedBytes() async {
    final db = await database;
    final result = await db.rawQuery('''
      SELECT SUM(total_bytes) as total 
      FROM downloads 
      WHERE status = ? AND total_bytes IS NOT NULL
    ''', [DownloadStatus.completed.index]);
    
    return result.first['total'] as int? ?? 0;
  }

  // Maintenance
  Future<void> clearOldDownloads({int daysOld = 30}) async {
    final db = await database;
    final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
    
    await db.delete(
      'downloads',
      where: 'created_at < ? AND status IN (?, ?)',
      whereArgs: [
        cutoffDate.toIso8601String(),
        DownloadStatus.completed.index,
        DownloadStatus.failed.index,
      ],
    );
  }

  Future<void> clearAllDownloads() async {
    final db = await database;
    await db.delete('downloads');
  }

  Future<void> vacuum() async {
    final db = await database;
    await db.execute('VACUUM');
  }

  // Backup and restore
  Future<List<Map<String, dynamic>>> exportDownloads() async {
    final db = await database;
    return await db.query('downloads');
  }

  Future<void> importDownloads(List<Map<String, dynamic>> downloads) async {
    final db = await database;
    final batch = db.batch();
    
    for (final download in downloads) {
      batch.insert('downloads', download, conflictAlgorithm: ConflictAlgorithm.replace);
    }
    
    await batch.commit();
  }

  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
