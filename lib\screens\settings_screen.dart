import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../providers/app_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/download_provider.dart';
import '../services/permission_service.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final downloadProvider = Provider.of<DownloadProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(appProvider.getString('settings')),
      ),
      
      body: ListView(
        children: [
          // App Settings Section
          _buildSectionHeader(context, 'App Settings'),
          
          // Theme Setting
          ListTile(
            leading: Icon(
              themeProvider.isDarkMode ? Icons.dark_mode : Icons.light_mode,
            ),
            title: Text(appProvider.getString('dark_mode')),
            subtitle: Text(
              themeProvider.themeMode == ThemeMode.system
                  ? 'System default'
                  : themeProvider.isDarkMode
                      ? 'Dark'
                      : 'Light',
            ),
            trailing: PopupMenuButton<ThemeMode>(
              icon: const Icon(Icons.arrow_drop_down),
              onSelected: (mode) => themeProvider.setThemeMode(mode),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: ThemeMode.system,
                  child: Text('System'),
                ),
                const PopupMenuItem(
                  value: ThemeMode.light,
                  child: Text('Light'),
                ),
                const PopupMenuItem(
                  value: ThemeMode.dark,
                  child: Text('Dark'),
                ),
              ],
            ),
          ),
          
          // Language Setting
          ListTile(
            leading: const Icon(Icons.language),
            title: Text(appProvider.getString('language')),
            subtitle: Text(
              appProvider.isArabic ? 'العربية' : 'English',
            ),
            trailing: PopupMenuButton<String>(
              icon: const Icon(Icons.arrow_drop_down),
              onSelected: (languageCode) => appProvider.setLanguage(languageCode),
              itemBuilder: (context) => AppProvider.supportedLanguages.entries
                  .map((entry) => PopupMenuItem(
                        value: entry.key,
                        child: Text(entry.value),
                      ))
                  .toList(),
            ),
          ),
          
          const Divider(),
          
          // Download Settings Section
          _buildSectionHeader(context, 'Download Settings'),
          
          // Download Statistics
          ListTile(
            leading: const Icon(Icons.analytics),
            title: const Text('Download Statistics'),
            subtitle: Text(_getStatsText(downloadProvider.getDownloadStats())),
            onTap: () => _showStatsDialog(context, downloadProvider),
          ),
          
          // Storage Usage
          ListTile(
            leading: const Icon(Icons.storage),
            title: const Text('Storage Usage'),
            subtitle: Text(_formatBytes(downloadProvider.getTotalDownloadedBytes())),
            onTap: () => _showStorageDialog(context, downloadProvider),
          ),
          
          const Divider(),
          
          // Permissions Section
          _buildSectionHeader(context, 'Permissions'),
          
          ListTile(
            leading: const Icon(Icons.security),
            title: const Text('App Permissions'),
            subtitle: const Text('Manage app permissions'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showPermissionsDialog(context),
          ),
          
          const Divider(),
          
          // Data Management Section
          _buildSectionHeader(context, 'Data Management'),
          
          ListTile(
            leading: const Icon(Icons.refresh),
            title: const Text('Refresh Downloads'),
            subtitle: const Text('Reload download list'),
            onTap: () => downloadProvider.refreshDownloads(),
          ),
          
          ListTile(
            leading: const Icon(Icons.clear_all),
            title: const Text('Clear Completed Downloads'),
            subtitle: const Text('Remove completed downloads from list'),
            onTap: () => _showClearCompletedDialog(context, downloadProvider),
          ),
          
          ListTile(
            leading: const Icon(Icons.delete_forever),
            title: const Text('Clear All Downloads'),
            subtitle: const Text('Remove all downloads and files'),
            onTap: () => _showClearAllDialog(context, downloadProvider),
          ),
          
          const Divider(),
          
          // About Section
          _buildSectionHeader(context, 'About'),
          
          ListTile(
            leading: const Icon(Icons.info),
            title: Text(appProvider.getString('about')),
            subtitle: const Text('Version 1.0.0'),
            onTap: () => _showAboutDialog(context),
          ),
          
          ListTile(
            leading: const Icon(Icons.privacy_tip),
            title: Text(appProvider.getString('privacy_policy')),
            trailing: const Icon(Icons.open_in_new),
            onTap: () => _launchUrl('https://example.com/privacy'),
          ),
          
          ListTile(
            leading: const Icon(Icons.description),
            title: Text(appProvider.getString('terms_of_service')),
            trailing: const Icon(Icons.open_in_new),
            onTap: () => _launchUrl('https://example.com/terms'),
          ),
          
          ListTile(
            leading: const Icon(Icons.contact_support),
            title: Text(appProvider.getString('contact_us')),
            trailing: const Icon(Icons.open_in_new),
            onTap: () => _launchUrl('mailto:<EMAIL>'),
          ),
          
          ListTile(
            leading: const Icon(Icons.star),
            title: Text(appProvider.getString('rate_app')),
            trailing: const Icon(Icons.open_in_new),
            onTap: () => _launchUrl('https://play.google.com/store/apps/details?id=com.example.download_videos'),
          ),
          
          ListTile(
            leading: const Icon(Icons.share),
            title: Text(appProvider.getString('share_app')),
            onTap: () => _shareApp(),
          ),
          
          const SizedBox(height: 20),
          
          // Footer
          Center(
            child: Column(
              children: [
                Text(
                  'Video Downloader Pro',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Made with ❤️ for video enthusiasts',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _getStatsText(Map<String, int> stats) {
    return '${stats['total']} total, ${stats['completed']} completed, ${stats['downloading']} active';
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }

  void _showStatsDialog(BuildContext context, DownloadProvider downloadProvider) {
    final stats = downloadProvider.getDownloadStats();
    final platformStats = downloadProvider.getPlatformStats();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Download Statistics'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Overall Statistics',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              _StatRow('Total Downloads', '${stats['total']}'),
              _StatRow('Completed', '${stats['completed']}'),
              _StatRow('Active', '${stats['downloading']}'),
              _StatRow('Failed', '${stats['failed']}'),
              _StatRow('Paused', '${stats['paused']}'),
              
              const SizedBox(height: 16),
              
              Text(
                'Platform Statistics',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...platformStats.entries.map((entry) => 
                _StatRow(entry.key, '${entry.value}')
              ),
              
              const SizedBox(height: 16),
              
              _StatRow('Total Downloaded', _formatBytes(downloadProvider.getTotalDownloadedBytes())),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showStorageDialog(BuildContext context, DownloadProvider downloadProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Storage Usage'),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Total Downloaded: ${_formatBytes(downloadProvider.getTotalDownloadedBytes())}',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              'Completed Downloads: ${downloadProvider.completedDownloads.length}',
            ),
            const SizedBox(height: 8),
            Text(
              'Average File Size: ${downloadProvider.completedDownloads.isNotEmpty ? _formatBytes(downloadProvider.getTotalDownloadedBytes() ~/ downloadProvider.completedDownloads.length) : '0B'}',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showPermissionsDialog(BuildContext context) async {
    final permissionService = PermissionService();
    final hasAll = await permissionService.hasAllPermissions();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('App Permissions'),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              hasAll ? 'All permissions granted ✓' : 'Some permissions missing ⚠️',
              style: TextStyle(
                color: hasAll ? Colors.green : Colors.orange,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text('Required permissions:'),
            const SizedBox(height: 8),
            ...permissionService.getPermissionExplanations().entries.map(
              (entry) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    const Icon(Icons.check_circle, size: 16, color: Colors.green),
                    const SizedBox(width: 8),
                    Expanded(child: Text(entry.key)),
                  ],
                ),
              ),
            ),
          ],
        ),
        actions: [
          if (!hasAll)
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await permissionService.requestAllPermissions();
              },
              child: const Text('Grant Permissions'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showClearCompletedDialog(BuildContext context, DownloadProvider downloadProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Completed Downloads'),
        content: const Text(
          'This will remove all completed downloads from the list. '
          'The downloaded files will remain on your device.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await downloadProvider.clearCompletedDownloads();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Completed downloads cleared')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showClearAllDialog(BuildContext context, DownloadProvider downloadProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Downloads'),
        content: const Text(
          'This will remove ALL downloads from the list and DELETE all downloaded files. '
          'This action cannot be undone!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await downloadProvider.clearAllDownloads();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('All downloads cleared')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete All'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'Video Downloader Pro',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Icon(
          Icons.video_library,
          color: Colors.white,
          size: 32,
        ),
      ),
      children: [
        const Text(
          'A powerful video downloader that supports multiple platforms '
          'and provides high-quality downloads without watermarks.',
        ),
        const SizedBox(height: 16),
        const Text(
          'Features:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const Text('• Download from YouTube, TikTok, Instagram, and more'),
        const Text('• Choose video quality and format'),
        const Text('• Audio-only downloads'),
        const Text('• Background downloads'),
        const Text('• No watermarks when possible'),
        const Text('• Dark mode support'),
        const Text('• Multiple languages'),
      ],
    );
  }

  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  void _shareApp() {
    // TODO: Implement app sharing
    // This would typically use the share_plus package
  }
}

class _StatRow extends StatelessWidget {
  final String label;
  final String value;

  const _StatRow(this.label, this.value);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
