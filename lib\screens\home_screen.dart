import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../providers/download_provider.dart';
import '../providers/theme_provider.dart';
import '../models/video_info.dart';
import '../services/platform_detector.dart';
import '../widgets/video_info_card.dart';
import '../widgets/quality_selector.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _urlController = TextEditingController();
  final FocusNode _urlFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _checkClipboard();
  }

  @override
  void dispose() {
    _urlController.dispose();
    _urlFocusNode.dispose();
    super.dispose();
  }

  Future<void> _checkClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null) {
        final text = clipboardData!.text!;
        if (PlatformDetector.isValidUrl(text)) {
          _showClipboardDialog(text);
        }
      }
    } catch (e) {
      // Ignore clipboard errors
    }
  }

  void _showClipboardDialog(String url) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('URL Detected'),
        content: Text('Found a video URL in clipboard:\n\n$url\n\nWould you like to use it?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _urlController.text = url;
              _extractVideoInfo();
            },
            child: const Text('Yes'),
          ),
        ],
      ),
    );
  }

  Future<void> _extractVideoInfo() async {
    final url = _urlController.text.trim();
    if (url.isEmpty) {
      _showSnackBar('Please enter a video URL', isError: true);
      return;
    }

    final downloadProvider = Provider.of<DownloadProvider>(context, listen: false);
    
    // Check if URL is already downloaded
    if (downloadProvider.isUrlAlreadyDownloaded(url)) {
      _showSnackBar('This video is already downloaded or being downloaded', isError: true);
      return;
    }

    final success = await downloadProvider.extractVideoInfo(url);
    
    if (!success && downloadProvider.extractionError != null) {
      _showSnackBar(downloadProvider.extractionError!, isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  Future<void> _pasteFromClipboard() async {
    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null) {
        _urlController.text = clipboardData!.text!;
      }
    } catch (e) {
      _showSnackBar('Failed to paste from clipboard', isError: true);
    }
  }

  void _clearUrl() {
    _urlController.clear();
    final downloadProvider = Provider.of<DownloadProvider>(context, listen: false);
    downloadProvider.clearCurrentVideoInfo();
  }

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    final downloadProvider = Provider.of<DownloadProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(appProvider.getString('app_name')),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.paste),
            onPressed: _pasteFromClipboard,
            tooltip: 'Paste from clipboard',
          ),
        ],
      ),
      
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // URL Input Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Enter Video URL',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    // URL Input Field
                    TextField(
                      controller: _urlController,
                      focusNode: _urlFocusNode,
                      decoration: InputDecoration(
                        hintText: appProvider.getString('paste_url'),
                        prefixIcon: const Icon(Icons.link),
                        suffixIcon: _urlController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: _clearUrl,
                              )
                            : null,
                      ),
                      keyboardType: TextInputType.url,
                      textInputAction: TextInputAction.done,
                      onSubmitted: (_) => _extractVideoInfo(),
                      onChanged: (value) => setState(() {}),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Extract Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: downloadProvider.isExtracting 
                            ? null 
                            : _extractVideoInfo,
                        icon: downloadProvider.isExtracting
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.search),
                        label: Text(
                          downloadProvider.isExtracting 
                              ? 'Extracting...' 
                              : 'Extract Video Info',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Supported Platforms
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Supported Platforms',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: SupportedPlatform.values
                          .where((p) => p != SupportedPlatform.unknown)
                          .map((platform) => Chip(
                            avatar: Text(
                              PlatformDetector.getPlatformIcon(platform),
                              style: const TextStyle(fontSize: 16),
                            ),
                            label: Text(
                              PlatformDetector.getPlatformName(platform),
                              style: const TextStyle(fontSize: 12),
                            ),
                          ))
                          .toList(),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Video Info Section
            if (downloadProvider.currentVideoInfo != null) ...[
              VideoInfoCard(
                videoInfo: downloadProvider.currentVideoInfo!,
                onDownload: (quality) => _startDownload(quality),
              ),
            ] else if (downloadProvider.extractionError != null) ...[
              Card(
                color: Colors.red.withOpacity(0.1),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 48,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Extraction Failed',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        downloadProvider.extractionError!,
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.red[700]),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          downloadProvider.clearExtractionError();
                          _extractVideoInfo();
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // Quick Tips
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          color: themeProvider.isDarkMode ? Colors.yellow : Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Quick Tips',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    
                    const _TipItem(
                      icon: Icons.copy,
                      text: 'Copy video URL from your browser or app',
                    ),
                    const _TipItem(
                      icon: Icons.paste,
                      text: 'Paste it in the URL field above',
                    ),
                    const _TipItem(
                      icon: Icons.high_quality,
                      text: 'Choose your preferred quality and format',
                    ),
                    const _TipItem(
                      icon: Icons.download,
                      text: 'Start downloading and enjoy!',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _startDownload(VideoQuality quality) async {
    final downloadProvider = Provider.of<DownloadProvider>(context, listen: false);
    
    final downloadId = await downloadProvider.startDownload(quality);
    
    if (downloadId != null) {
      _showSnackBar('Download started successfully!');
      _clearUrl();
      
      // Switch to downloads tab
      // This would require communication with parent widget
    } else {
      _showSnackBar('Failed to start download', isError: true);
    }
  }
}

class _TipItem extends StatelessWidget {
  final IconData icon;
  final String text;

  const _TipItem({
    required this.icon,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
