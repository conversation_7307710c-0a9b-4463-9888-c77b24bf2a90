import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../providers/theme_provider.dart';
import '../services/permission_service.dart';
import 'main_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final PermissionService _permissionService = PermissionService();

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'Welcome to Video Downloader Pro',
      description: 'Download videos from your favorite platforms with ease',
      icon: Icons.video_library,
      color: ThemeProvider.primaryBlue,
    ),
    OnboardingPage(
      title: 'Multiple Platforms',
      description: 'Support for YouTube, TikTok, Instagram, Facebook and more',
      icon: Icons.language,
      color: ThemeProvider.accentGreen,
    ),
    OnboardingPage(
      title: 'Choose Quality',
      description: 'Select video quality and format that suits your needs',
      icon: Icons.high_quality,
      color: ThemeProvider.warningOrange,
    ),
    OnboardingPage(
      title: 'No Watermarks',
      description: 'Download videos without watermarks when possible',
      icon: Icons.clear,
      color: ThemeProvider.secondaryBlue,
    ),
    OnboardingPage(
      title: 'Permissions Required',
      description: 'We need some permissions to provide the best experience',
      icon: Icons.security,
      color: ThemeProvider.errorRed,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _requestPermissions();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipToEnd() {
    _pageController.animateToPage(
      _pages.length - 1,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _requestPermissions() async {
    final results = await _permissionService.requestPermissionsWithFeedback();
    
    // Show results dialog
    if (mounted) {
      await _showPermissionResults(results);
    }
    
    _completeOnboarding();
  }

  Future<void> _showPermissionResults(Map<String, bool> results) async {
    final granted = results.values.where((v) => v).length;
    final total = results.length;
    
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Permissions Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('$granted of $total permissions granted'),
            const SizedBox(height: 16),
            ...results.entries.map((entry) => ListTile(
              leading: Icon(
                entry.value ? Icons.check_circle : Icons.cancel,
                color: entry.value ? Colors.green : Colors.red,
              ),
              title: Text(entry.key),
              dense: true,
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  void _completeOnboarding() async {
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    
    await appProvider.setFirstLaunchCompleted();
    await appProvider.setOnboardingCompleted();
    
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const MainScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
              },
              itemCount: _pages.length,
              itemBuilder: (context, index) {
                return _buildPage(_pages[index]);
              },
            ),
          ),
          
          // Bottom navigation
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Page indicator
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    _pages.length,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: _currentPage == index ? 24 : 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _currentPage == index
                            ? ThemeProvider.primaryBlue
                            : Colors.grey.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Navigation buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Skip/Previous button
                    TextButton(
                      onPressed: _currentPage == 0 ? _skipToEnd : _previousPage,
                      child: Text(
                        _currentPage == 0 
                            ? appProvider.getString('skip')
                            : 'Previous',
                      ),
                    ),
                    
                    // Next/Finish button
                    ElevatedButton(
                      onPressed: _nextPage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _pages[_currentPage].color,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 32,
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        _currentPage == _pages.length - 1
                            ? appProvider.getString('get_started')
                            : appProvider.getString('next'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: page.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              page.icon,
              size: 60,
              color: page.color,
            ),
          ),
          
          const SizedBox(height: 40),
          
          // Title
          Text(
            page.title,
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 20),
          
          // Description
          Text(
            page.description,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Special content for permissions page
          if (_currentPage == _pages.length - 1) ...[
            const SizedBox(height: 30),
            _buildPermissionsList(),
          ],
        ],
      ),
    );
  }

  Widget _buildPermissionsList() {
    final permissions = _permissionService.getPermissionExplanations();
    
    return Column(
      children: permissions.entries.map((entry) {
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: ListTile(
            leading: Icon(
              _getPermissionIcon(entry.key),
              color: ThemeProvider.primaryBlue,
            ),
            title: Text(
              entry.key,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Text(
              entry.value,
              style: const TextStyle(fontSize: 12),
            ),
            dense: true,
          ),
        );
      }).toList(),
    );
  }

  IconData _getPermissionIcon(String permission) {
    switch (permission.toLowerCase()) {
      case 'storage':
        return Icons.storage;
      case 'notifications':
        return Icons.notifications;
      case 'network':
        return Icons.wifi;
      case 'media':
        return Icons.perm_media;
      default:
        return Icons.security;
    }
  }
}

class OnboardingPage {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  OnboardingPage({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
