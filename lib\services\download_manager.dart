import 'dart:io';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/download_item.dart';
import '../models/video_info.dart';
import '../models/video_quality.dart';
import 'database_service.dart';
import 'notification_service.dart';
import 'local_download_service.dart';

class DownloadManager {
  static final DownloadManager _instance = DownloadManager._internal();
  factory DownloadManager() => _instance;
  DownloadManager._internal();

  final Dio _dio = Dio();
  final Map<String, CancelToken> _cancelTokens = {};
  final Map<String, DownloadItem> _activeDownloads = {};
  final DatabaseService _db = DatabaseService();
  final NotificationService _notifications = NotificationService();

  // Stream controller for download progress updates
  final Map<String, Function(DownloadItem)> _progressCallbacks = {};

  Future<void> initialize() async {
    await _db.initialize();
    await _notifications.initialize();

    // Configure Dio
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.headers = {
      'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    };
  }

  Future<String> startDownload({
    required VideoInfo videoInfo,
    required VideoQuality selectedQuality,
    Function(DownloadItem)? onProgress,
  }) async {
    final downloadId = DateTime.now().millisecondsSinceEpoch.toString();

    // Create download directory
    final downloadDir = await _getDownloadDirectory();
    final fileName = _generateFileName(videoInfo.title, selectedQuality.format);
    final filePath = path.join(downloadDir.path, fileName);

    // Create download item
    final downloadItem = DownloadItem(
      id: downloadId,
      videoId: videoInfo.id,
      title: videoInfo.title,
      url: videoInfo.url,
      thumbnailUrl: videoInfo.thumbnailUrl,
      platform: videoInfo.platform,
      quality: selectedQuality.quality,
      format: selectedQuality.format,
      filePath: filePath,
      createdAt: DateTime.now(),
    );

    // Save to database
    await _db.insertDownload(downloadItem);

    // Register progress callback
    if (onProgress != null) {
      _progressCallbacks[downloadId] = onProgress;
    }

    // Start download in background
    _startBackgroundDownload(downloadItem, selectedQuality.downloadUrl);

    return downloadId;
  }

  Future<void> _startBackgroundDownload(
    DownloadItem item,
    String downloadUrl,
  ) async {
    final cancelToken = CancelToken();
    _cancelTokens[item.id] = cancelToken;
    _activeDownloads[item.id] = item;

    try {
      // Update status to downloading
      final updatedItem = item.copyWith(status: DownloadStatus.downloading);
      await _updateDownloadItem(updatedItem);

      // Show notification
      await _notifications.showDownloadStarted(item.title);

      // Get actual download URL if it's a conversion URL
      String actualDownloadUrl = downloadUrl;

      // Handle empty or invalid download URLs by trying multiple fallbacks
      if (downloadUrl.isEmpty || downloadUrl == item.url) {
        print('Empty download URL detected, trying fallback methods...');

        // Try Cobalt API first
        final cobaltUrl = await _getCobaltDownloadUrl(item.url);
        if (cobaltUrl != null && cobaltUrl.isNotEmpty) {
          actualDownloadUrl = cobaltUrl;
          print('Got Cobalt URL: $actualDownloadUrl');
        } else {
          // Try alternative extraction methods
          print('Cobalt failed, trying alternative methods...');
          final alternativeUrl = await _getAlternativeDownloadUrl(item.url);
          if (alternativeUrl != null && alternativeUrl.isNotEmpty) {
            actualDownloadUrl = alternativeUrl;
            print('Got alternative URL: $actualDownloadUrl');
          } else {
            throw Exception('No valid download URL available from any source');
          }
        }
      }
      // Check if this is a conversion URL that needs processing
      else if (downloadUrl.contains('y2mate.com') ||
          downloadUrl.contains('yt1s.com')) {
        print('Converting download URL: $downloadUrl');
        final convertedUrl = await _getActualDownloadUrl(downloadUrl);
        if (convertedUrl != null && convertedUrl.isNotEmpty) {
          actualDownloadUrl = convertedUrl;
          print('Converted to: $actualDownloadUrl');
        } else {
          throw Exception('Failed to get actual download URL');
        }
      }

      // Validate the download URL
      if (actualDownloadUrl.isEmpty || !actualDownloadUrl.startsWith('http')) {
        throw Exception('Invalid download URL: $actualDownloadUrl');
      }

      // Start download with retry mechanism
      print('Starting download from: $actualDownloadUrl');
      print('Saving to: ${item.filePath}');

      await _dio.download(
        actualDownloadUrl,
        item.filePath,
        cancelToken: cancelToken,
        options: Options(
          headers: {
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': _getRefererForUrl(actualDownloadUrl),
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Connection': 'keep-alive',
          },
          followRedirects: true,
          maxRedirects: 5,
          receiveTimeout: const Duration(minutes: 10),
          sendTimeout: const Duration(seconds: 30),
        ),
        onReceiveProgress: (received, total) async {
          if (total != -1) {
            final progress = received / total;
            final updatedItem = _activeDownloads[item.id]!.copyWith(
              downloadedBytes: received,
              totalBytes: total,
              progress: progress,
            );

            await _updateDownloadItem(updatedItem);

            // Update notification
            await _notifications.updateDownloadProgress(
              item.title,
              (progress * 100).toInt(),
            );

            // Log progress for debugging
            if (received % (1024 * 1024) == 0 || progress == 1.0) {
              print(
                'Download progress: ${(progress * 100).toStringAsFixed(1)}% (${received}/${total} bytes)',
              );
            }
          }
        },
      );

      // Download completed
      final completedItem = _activeDownloads[item.id]!.copyWith(
        status: DownloadStatus.completed,
        completedAt: DateTime.now(),
        progress: 1.0,
      );

      await _updateDownloadItem(completedItem);
      await _notifications.showDownloadCompleted(item.title);
      print('Download completed successfully: ${item.title}');
    } catch (e) {
      print('Download error: $e');

      if (e is DioException && e.type == DioExceptionType.cancel) {
        // Download was cancelled
        final cancelledItem = _activeDownloads[item.id]!.copyWith(
          status: DownloadStatus.cancelled,
        );
        await _updateDownloadItem(cancelledItem);
        print('Download cancelled: ${item.title}');
      } else {
        // Download failed
        String errorMessage = 'Unknown error';
        if (e is DioException) {
          switch (e.type) {
            case DioExceptionType.connectionTimeout:
              errorMessage = 'Connection timeout';
              break;
            case DioExceptionType.receiveTimeout:
              errorMessage = 'Receive timeout';
              break;
            case DioExceptionType.badResponse:
              errorMessage = 'Server error: ${e.response?.statusCode}';
              break;
            case DioExceptionType.connectionError:
              errorMessage = 'Connection error';
              break;
            default:
              errorMessage = e.message ?? 'Network error';
          }
        } else {
          errorMessage = e.toString();
        }

        final failedItem = _activeDownloads[item.id]!.copyWith(
          status: DownloadStatus.failed,
          errorMessage: errorMessage,
        );
        await _updateDownloadItem(failedItem);
        await _notifications.showDownloadFailed(item.title);
        print('Download failed: ${item.title} - $errorMessage');
      }
    } finally {
      _cancelTokens.remove(item.id);
      _activeDownloads.remove(item.id);
      _progressCallbacks.remove(item.id);
    }
  }

  Future<void> pauseDownload(String downloadId) async {
    final cancelToken = _cancelTokens[downloadId];
    if (cancelToken != null) {
      cancelToken.cancel();

      final item = _activeDownloads[downloadId];
      if (item != null) {
        final pausedItem = item.copyWith(status: DownloadStatus.paused);
        await _updateDownloadItem(pausedItem);
      }
    }
  }

  Future<void> resumeDownload(String downloadId) async {
    final item = await _db.getDownload(downloadId);
    if (item != null && item.status == DownloadStatus.paused) {
      // Get video info again to get fresh download URL
      // This would require re-extracting the video info
      // For now, we'll just mark it as pending
      final resumedItem = item.copyWith(status: DownloadStatus.pending);
      await _updateDownloadItem(resumedItem);
    }
  }

  Future<void> cancelDownload(String downloadId) async {
    final cancelToken = _cancelTokens[downloadId];
    if (cancelToken != null) {
      cancelToken.cancel();
    }

    final item =
        _activeDownloads[downloadId] ?? await _db.getDownload(downloadId);
    if (item != null) {
      // Delete partial file
      final file = File(item.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      final cancelledItem = item.copyWith(status: DownloadStatus.cancelled);
      await _updateDownloadItem(cancelledItem);
    }
  }

  Future<void> deleteDownload(String downloadId) async {
    await cancelDownload(downloadId);

    final item = await _db.getDownload(downloadId);
    if (item != null) {
      // Delete file
      final file = File(item.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      // Remove from database
      await _db.deleteDownload(downloadId);
    }
  }

  Future<List<DownloadItem>> getAllDownloads() async {
    return await _db.getAllDownloads();
  }

  Future<List<DownloadItem>> getDownloadsByStatus(DownloadStatus status) async {
    return await _db.getDownloadsByStatus(status);
  }

  Future<DownloadItem?> getDownload(String downloadId) async {
    return await _db.getDownload(downloadId);
  }

  Future<void> _updateDownloadItem(DownloadItem item) async {
    _activeDownloads[item.id] = item;
    await _db.updateDownload(item);

    // Notify progress callback
    final callback = _progressCallbacks[item.id];
    if (callback != null) {
      callback(item);
    }
  }

  Future<Directory> _getDownloadDirectory() async {
    Directory? directory;

    if (Platform.isAndroid) {
      // Try to get external storage directory
      directory = await getExternalStorageDirectory();
      if (directory != null) {
        directory = Directory(path.join(directory.path, 'VideoDownloads'));
      }
    }

    // Fallback to app documents directory
    directory ??= await getApplicationDocumentsDirectory();
    directory = Directory(path.join(directory.path, 'Downloads'));

    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }

    return directory;
  }

  String _generateFileName(String title, String format) {
    // Clean title for filename
    String cleanTitle =
        title
            .replaceAll(RegExp(r'[<>:"/\\|?*]'), '')
            .replaceAll(RegExp(r'\s+'), '_')
            .trim();

    if (cleanTitle.length > 50) {
      cleanTitle = cleanTitle.substring(0, 50);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${cleanTitle}_$timestamp.$format';
  }

  // Utility methods
  bool isDownloading(String downloadId) {
    return _activeDownloads.containsKey(downloadId);
  }

  double getDownloadProgress(String downloadId) {
    final item = _activeDownloads[downloadId];
    return item?.progress ?? 0.0;
  }

  Future<void> clearCompletedDownloads() async {
    final completed = await getDownloadsByStatus(DownloadStatus.completed);
    for (final item in completed) {
      await _db.deleteDownload(item.id);
    }
  }

  Future<void> retryFailedDownloads() async {
    final failed = await getDownloadsByStatus(DownloadStatus.failed);
    for (final item in failed) {
      // Mark as pending for retry
      final retryItem = item.copyWith(
        status: DownloadStatus.pending,
        errorMessage: null,
      );
      await _updateDownloadItem(retryItem);
    }
  }

  // Get actual download URL from conversion APIs
  Future<String?> _getActualDownloadUrl(String conversionUrl) async {
    try {
      if (conversionUrl.contains('y2mate.com')) {
        return await _getY2mateDownloadUrl(conversionUrl);
      } else if (conversionUrl.contains('yt1s.com')) {
        return await _getYT1SDownloadUrl(conversionUrl);
      }
    } catch (e) {
      // Return original URL if conversion fails
    }
    return null;
  }

  // Alternative download URL extraction method
  Future<String?> _getAlternativeDownloadUrl(String videoUrl) async {
    try {
      // Try multiple alternative APIs
      final apis = [
        'https://api.savefrom.net/info',
        'https://api.y2mate.com/v1/info',
        'https://api.loader.to/ajax/search',
      ];

      for (final apiUrl in apis) {
        try {
          final response = await _dio.post(
            apiUrl,
            data: {'url': videoUrl},
            options: Options(
              headers: {
                'Content-Type': 'application/json',
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              },
              receiveTimeout: const Duration(seconds: 10),
            ),
          );

          if (response.statusCode == 200 && response.data != null) {
            final data = response.data;
            if (data is Map) {
              // Try different response formats
              if (data['url'] != null) return data['url'].toString();
              if (data['download_url'] != null)
                return data['download_url'].toString();
              if (data['dlink'] != null) return data['dlink'].toString();
              if (data['data'] != null && data['data']['url'] != null) {
                return data['data']['url'].toString();
              }
            }
          }
        } catch (e) {
          // Try next API
          continue;
        }
      }
    } catch (e) {
      // All alternatives failed
    }
    return null;
  }

  // Fallback method using Cobalt API
  Future<String?> _getCobaltDownloadUrl(String videoUrl) async {
    try {
      final response = await _dio.post(
        'https://api.cobalt.tools/api/json',
        data: {
          'url': videoUrl,
          'vQuality': '720',
          'vCodec': 'h264',
          'vFormat': 'mp4',
          'aFormat': 'mp3',
          'isAudioOnly': false,
          'isNoTTWatermark': true,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data is Map &&
            (data['status'] == 'success' || data['status'] == 'redirect')) {
          return data['url'];
        }
      }
    } catch (e) {
      print('Cobalt fallback error: $e');
    }
    return null;
  }

  // Get download URL from Y2mate conversion
  Future<String?> _getY2mateDownloadUrl(String conversionUrl) async {
    try {
      // Extract the k parameter from the URL
      final uri = Uri.parse(conversionUrl);
      final k = uri.queryParameters['k'];
      if (k == null) return null;

      final response = await _dio.post(
        'https://www.y2mate.com/mates/en68/ajax/convert',
        data: 'k=$k',
        options: Options(
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://www.y2mate.com/',
            'X-Requested-With': 'XMLHttpRequest',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data is Map && data['status'] == 'ok' && data['dlink'] != null) {
          return data['dlink'];
        }
      }
    } catch (e) {
      // Continue with original URL
      print('Y2mate conversion error: $e');
    }
    return null;
  }

  // Get download URL from YT1S conversion
  Future<String?> _getYT1SDownloadUrl(String conversionUrl) async {
    try {
      // Extract the k parameter from the URL
      final uri = Uri.parse(conversionUrl);
      final k = uri.queryParameters['k'];
      if (k == null) return null;

      final response = await _dio.post(
        'https://yt1s.com/api/ajaxConvert/convert',
        data: 'k=$k',
        options: Options(
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://yt1s.com/',
            'X-Requested-With': 'XMLHttpRequest',
          },
        ),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data is Map && data['status'] == 'ok' && data['dlink'] != null) {
          return data['dlink'];
        }
      }
    } catch (e) {
      // Continue with original URL
      print('YT1S conversion error: $e');
    }
    return null;
  }

  // Get appropriate referer for download URL
  String _getRefererForUrl(String url) {
    if (url.contains('y2mate.com')) {
      return 'https://www.y2mate.com/';
    } else if (url.contains('yt1s.com')) {
      return 'https://yt1s.com/';
    } else if (url.contains('tikwm.com')) {
      return 'https://tikwm.com/';
    } else if (url.contains('cobalt')) {
      return 'https://cobalt.tools/';
    }
    return 'https://www.google.com/';
  }

  void dispose() {
    // Cancel all active downloads
    for (final cancelToken in _cancelTokens.values) {
      cancelToken.cancel();
    }
    _cancelTokens.clear();
    _activeDownloads.clear();
    _progressCallbacks.clear();
  }
}
