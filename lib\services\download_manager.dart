import 'dart:io';
import 'dart:isolate';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/download_item.dart';
import '../models/video_info.dart';
import 'database_service.dart';
import 'notification_service.dart';

class DownloadManager {
  static final DownloadManager _instance = DownloadManager._internal();
  factory DownloadManager() => _instance;
  DownloadManager._internal();

  final Dio _dio = Dio();
  final Map<String, CancelToken> _cancelTokens = {};
  final Map<String, DownloadItem> _activeDownloads = {};
  final DatabaseService _db = DatabaseService();
  final NotificationService _notifications = NotificationService();

  // Stream controller for download progress updates
  final Map<String, Function(DownloadItem)> _progressCallbacks = {};

  Future<void> initialize() async {
    await _db.initialize();
    await _notifications.initialize();
    
    // Configure Dio
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.headers = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    };
  }

  Future<String> startDownload({
    required VideoInfo videoInfo,
    required VideoQuality selectedQuality,
    Function(DownloadItem)? onProgress,
  }) async {
    final downloadId = DateTime.now().millisecondsSinceEpoch.toString();
    
    // Create download directory
    final downloadDir = await _getDownloadDirectory();
    final fileName = _generateFileName(videoInfo.title, selectedQuality.format);
    final filePath = path.join(downloadDir.path, fileName);

    // Create download item
    final downloadItem = DownloadItem(
      id: downloadId,
      videoId: videoInfo.id,
      title: videoInfo.title,
      url: videoInfo.url,
      thumbnailUrl: videoInfo.thumbnailUrl,
      platform: videoInfo.platform,
      quality: selectedQuality.quality,
      format: selectedQuality.format,
      filePath: filePath,
      createdAt: DateTime.now(),
    );

    // Save to database
    await _db.insertDownload(downloadItem);
    
    // Register progress callback
    if (onProgress != null) {
      _progressCallbacks[downloadId] = onProgress;
    }

    // Start download in background
    _startBackgroundDownload(downloadItem, selectedQuality.downloadUrl);
    
    return downloadId;
  }

  Future<void> _startBackgroundDownload(DownloadItem item, String downloadUrl) async {
    final cancelToken = CancelToken();
    _cancelTokens[item.id] = cancelToken;
    _activeDownloads[item.id] = item;

    try {
      // Update status to downloading
      final updatedItem = item.copyWith(status: DownloadStatus.downloading);
      await _updateDownloadItem(updatedItem);

      // Show notification
      await _notifications.showDownloadStarted(item.title);

      // Start download
      await _dio.download(
        downloadUrl,
        item.filePath,
        cancelToken: cancelToken,
        onReceiveProgress: (received, total) async {
          if (total != -1) {
            final progress = received / total;
            final updatedItem = _activeDownloads[item.id]!.copyWith(
              downloadedBytes: received,
              totalBytes: total,
              progress: progress,
            );
            
            await _updateDownloadItem(updatedItem);
            
            // Update notification
            await _notifications.updateDownloadProgress(
              item.title,
              (progress * 100).toInt(),
            );
          }
        },
      );

      // Download completed
      final completedItem = _activeDownloads[item.id]!.copyWith(
        status: DownloadStatus.completed,
        completedAt: DateTime.now(),
        progress: 1.0,
      );
      
      await _updateDownloadItem(completedItem);
      await _notifications.showDownloadCompleted(item.title);
      
    } catch (e) {
      if (e is DioException && e.type == DioExceptionType.cancel) {
        // Download was cancelled
        final cancelledItem = _activeDownloads[item.id]!.copyWith(
          status: DownloadStatus.cancelled,
        );
        await _updateDownloadItem(cancelledItem);
      } else {
        // Download failed
        final failedItem = _activeDownloads[item.id]!.copyWith(
          status: DownloadStatus.failed,
          errorMessage: e.toString(),
        );
        await _updateDownloadItem(failedItem);
        await _notifications.showDownloadFailed(item.title);
      }
    } finally {
      _cancelTokens.remove(item.id);
      _activeDownloads.remove(item.id);
      _progressCallbacks.remove(item.id);
    }
  }

  Future<void> pauseDownload(String downloadId) async {
    final cancelToken = _cancelTokens[downloadId];
    if (cancelToken != null) {
      cancelToken.cancel();
      
      final item = _activeDownloads[downloadId];
      if (item != null) {
        final pausedItem = item.copyWith(status: DownloadStatus.paused);
        await _updateDownloadItem(pausedItem);
      }
    }
  }

  Future<void> resumeDownload(String downloadId) async {
    final item = await _db.getDownload(downloadId);
    if (item != null && item.status == DownloadStatus.paused) {
      // Get video info again to get fresh download URL
      // This would require re-extracting the video info
      // For now, we'll just mark it as pending
      final resumedItem = item.copyWith(status: DownloadStatus.pending);
      await _updateDownloadItem(resumedItem);
    }
  }

  Future<void> cancelDownload(String downloadId) async {
    final cancelToken = _cancelTokens[downloadId];
    if (cancelToken != null) {
      cancelToken.cancel();
    }
    
    final item = _activeDownloads[downloadId] ?? await _db.getDownload(downloadId);
    if (item != null) {
      // Delete partial file
      final file = File(item.filePath);
      if (await file.exists()) {
        await file.delete();
      }
      
      final cancelledItem = item.copyWith(status: DownloadStatus.cancelled);
      await _updateDownloadItem(cancelledItem);
    }
  }

  Future<void> deleteDownload(String downloadId) async {
    await cancelDownload(downloadId);
    
    final item = await _db.getDownload(downloadId);
    if (item != null) {
      // Delete file
      final file = File(item.filePath);
      if (await file.exists()) {
        await file.delete();
      }
      
      // Remove from database
      await _db.deleteDownload(downloadId);
    }
  }

  Future<List<DownloadItem>> getAllDownloads() async {
    return await _db.getAllDownloads();
  }

  Future<List<DownloadItem>> getDownloadsByStatus(DownloadStatus status) async {
    return await _db.getDownloadsByStatus(status);
  }

  Future<DownloadItem?> getDownload(String downloadId) async {
    return await _db.getDownload(downloadId);
  }

  Future<void> _updateDownloadItem(DownloadItem item) async {
    _activeDownloads[item.id] = item;
    await _db.updateDownload(item);
    
    // Notify progress callback
    final callback = _progressCallbacks[item.id];
    if (callback != null) {
      callback(item);
    }
  }

  Future<Directory> _getDownloadDirectory() async {
    Directory? directory;
    
    if (Platform.isAndroid) {
      // Try to get external storage directory
      directory = await getExternalStorageDirectory();
      if (directory != null) {
        directory = Directory(path.join(directory.path, 'VideoDownloads'));
      }
    }
    
    // Fallback to app documents directory
    directory ??= await getApplicationDocumentsDirectory();
    directory = Directory(path.join(directory.path, 'Downloads'));
    
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    
    return directory;
  }

  String _generateFileName(String title, String format) {
    // Clean title for filename
    String cleanTitle = title
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .trim();
    
    if (cleanTitle.length > 50) {
      cleanTitle = cleanTitle.substring(0, 50);
    }
    
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${cleanTitle}_$timestamp.$format';
  }

  // Utility methods
  bool isDownloading(String downloadId) {
    return _activeDownloads.containsKey(downloadId);
  }

  double getDownloadProgress(String downloadId) {
    final item = _activeDownloads[downloadId];
    return item?.progress ?? 0.0;
  }

  Future<void> clearCompletedDownloads() async {
    final completed = await getDownloadsByStatus(DownloadStatus.completed);
    for (final item in completed) {
      await _db.deleteDownload(item.id);
    }
  }

  Future<void> retryFailedDownloads() async {
    final failed = await getDownloadsByStatus(DownloadStatus.failed);
    for (final item in failed) {
      // Mark as pending for retry
      final retryItem = item.copyWith(
        status: DownloadStatus.pending,
        errorMessage: null,
      );
      await _updateDownloadItem(retryItem);
    }
  }

  void dispose() {
    // Cancel all active downloads
    for (final cancelToken in _cancelTokens.values) {
      cancelToken.cancel();
    }
    _cancelTokens.clear();
    _activeDownloads.clear();
    _progressCallbacks.clear();
  }
}
