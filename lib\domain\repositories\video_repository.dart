import '../../core/error/failures.dart';
import '../../core/utils/either.dart';
import '../entities/video_info.dart';

/// Repository interface for video operations
abstract class VideoRepository {
  /// Extract video information from URL
  Future<Either<Failure, VideoInfo>> extractVideoInfo(String url);

  /// Get cached video information
  Future<Either<Failure, VideoInfo?>> getCachedVideoInfo(String url);

  /// Cache video information
  Future<Either<Failure, void>> cacheVideoInfo(VideoInfo videoInfo);

  /// Clear video cache
  Future<Either<Failure, void>> clearVideoCache();

  /// Get supported platforms
  List<String> getSupportedPlatforms();

  /// Check if URL is supported
  bool isUrlSupported(String url);

  /// Detect platform from URL
  String detectPlatform(String url);

  /// Validate video URL
  Either<Failure, String> validateUrl(String url);
}
