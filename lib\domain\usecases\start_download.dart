import 'package:injectable/injectable.dart';

import '../../core/error/failures.dart';
import '../../core/utils/either.dart';
import '../entities/video_info.dart';
import '../entities/video_quality.dart';
import '../repositories/download_repository.dart';

/// Use case for starting a download
@injectable
class StartDownload {
  const StartDownload(this._repository);

  final DownloadRepository _repository;

  /// Execute the use case
  Future<Either<Failure, String>> call(StartDownloadParams params) async {
    // Validate video info
    if (params.videoInfo.availableQualities.isEmpty) {
      return const Left(
        ValidationFailure(
          message: 'No available qualities for download',
          code: 'NO_QUALITIES',
        ),
      );
    }

    // Validate selected quality
    if (!params.videoInfo.availableQualities.contains(params.quality)) {
      return const Left(
        ValidationFailure(
          message: 'Selected quality is not available',
          code: 'INVALID_QUALITY',
        ),
      );
    }

    // Check if download URL is valid
    if (params.quality.downloadUrl.isEmpty) {
      return const Left(
        ValidationFailure(
          message: 'Download URL is empty',
          code: 'EMPTY_DOWNLOAD_URL',
        ),
      );
    }

    // Start the download
    return await _repository.startDownload(
      videoInfo: params.videoInfo,
      quality: params.quality,
    );
  }
}

/// Parameters for StartDownload use case
class StartDownloadParams {
  const StartDownloadParams({
    required this.videoInfo,
    required this.quality,
  });

  final VideoInfo videoInfo;
  final VideoQuality quality;
}
