enum SupportedPlatform {
  youtube,
  tiktok,
  instagram,
  facebook,
  twitter,
  snapchat,
  unknown,
}

class PlatformDetector {
  static SupportedPlatform detectPlatform(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return SupportedPlatform.unknown;

    final host = uri.host.toLowerCase();
    
    // YouTube detection
    if (host.contains('youtube.com') || 
        host.contains('youtu.be') || 
        host.contains('m.youtube.com')) {
      return SupportedPlatform.youtube;
    }
    
    // TikTok detection
    if (host.contains('tiktok.com') || 
        host.contains('vm.tiktok.com') ||
        host.contains('vt.tiktok.com')) {
      return SupportedPlatform.tiktok;
    }
    
    // Instagram detection
    if (host.contains('instagram.com') || 
        host.contains('instagr.am')) {
      return SupportedPlatform.instagram;
    }
    
    // Facebook detection
    if (host.contains('facebook.com') || 
        host.contains('fb.watch') ||
        host.contains('fb.com')) {
      return SupportedPlatform.facebook;
    }
    
    // Twitter detection
    if (host.contains('twitter.com') || 
        host.contains('t.co') ||
        host.contains('x.com')) {
      return SupportedPlatform.twitter;
    }
    
    // Snapchat detection
    if (host.contains('snapchat.com')) {
      return SupportedPlatform.snapchat;
    }
    
    return SupportedPlatform.unknown;
  }

  static String getPlatformName(SupportedPlatform platform) {
    switch (platform) {
      case SupportedPlatform.youtube:
        return 'Video Platform 1';
      case SupportedPlatform.tiktok:
        return 'Short Video Platform';
      case SupportedPlatform.instagram:
        return 'Photo Platform';
      case SupportedPlatform.facebook:
        return 'Social Platform 1';
      case SupportedPlatform.twitter:
        return 'Social Platform 2';
      case SupportedPlatform.snapchat:
        return 'Story Platform';
      case SupportedPlatform.unknown:
        return 'Unknown Platform';
    }
  }

  static String getPlatformIcon(SupportedPlatform platform) {
    switch (platform) {
      case SupportedPlatform.youtube:
        return '🎥';
      case SupportedPlatform.tiktok:
        return '🎵';
      case SupportedPlatform.instagram:
        return '📷';
      case SupportedPlatform.facebook:
        return '👥';
      case SupportedPlatform.twitter:
        return '🐦';
      case SupportedPlatform.snapchat:
        return '👻';
      case SupportedPlatform.unknown:
        return '❓';
    }
  }

  static bool isValidUrl(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return false;
    
    return uri.hasScheme && 
           (uri.scheme == 'http' || uri.scheme == 'https') &&
           uri.hasAuthority;
  }

  static String? extractVideoId(String url, SupportedPlatform platform) {
    final uri = Uri.tryParse(url);
    if (uri == null) return null;

    switch (platform) {
      case SupportedPlatform.youtube:
        return _extractYouTubeId(uri);
      case SupportedPlatform.tiktok:
        return _extractTikTokId(uri);
      case SupportedPlatform.instagram:
        return _extractInstagramId(uri);
      case SupportedPlatform.facebook:
        return _extractFacebookId(uri);
      case SupportedPlatform.twitter:
        return _extractTwitterId(uri);
      default:
        return null;
    }
  }

  static String? _extractYouTubeId(Uri uri) {
    if (uri.host.contains('youtu.be')) {
      return uri.pathSegments.isNotEmpty ? uri.pathSegments[0] : null;
    }
    
    if (uri.queryParameters.containsKey('v')) {
      return uri.queryParameters['v'];
    }
    
    final path = uri.path;
    if (path.startsWith('/watch')) {
      return uri.queryParameters['v'];
    } else if (path.startsWith('/embed/')) {
      return path.split('/')[2];
    }
    
    return null;
  }

  static String? _extractTikTokId(Uri uri) {
    final path = uri.path;
    final segments = path.split('/');
    
    for (int i = 0; i < segments.length; i++) {
      if (segments[i] == 'video' && i + 1 < segments.length) {
        return segments[i + 1];
      }
    }
    
    return null;
  }

  static String? _extractInstagramId(Uri uri) {
    final path = uri.path;
    final segments = path.split('/');
    
    for (int i = 0; i < segments.length; i++) {
      if ((segments[i] == 'p' || segments[i] == 'reel') && 
          i + 1 < segments.length) {
        return segments[i + 1];
      }
    }
    
    return null;
  }

  static String? _extractFacebookId(Uri uri) {
    final path = uri.path;
    if (path.contains('/videos/')) {
      final segments = path.split('/');
      final videoIndex = segments.indexOf('videos');
      if (videoIndex != -1 && videoIndex + 1 < segments.length) {
        return segments[videoIndex + 1];
      }
    }
    
    return uri.queryParameters['v'];
  }

  static String? _extractTwitterId(Uri uri) {
    final path = uri.path;
    final segments = path.split('/');
    
    for (int i = 0; i < segments.length; i++) {
      if (segments[i] == 'status' && i + 1 < segments.length) {
        return segments[i + 1];
      }
    }
    
    return null;
  }
}
