import 'package:flutter/material.dart';
import '../models/download_item.dart';
import '../providers/theme_provider.dart';

class DownloadItemCard extends StatelessWidget {
  final DownloadItem download;
  final Function(String) onAction;

  const DownloadItemCard({
    super.key,
    required this.download,
    required this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Column(
        children: [
          // Main content
          ListTile(
            leading: _buildLeadingIcon(),
            title: Text(
              download.title,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                _buildSubtitleRow(),
                const SizedBox(height: 8),
                _buildProgressIndicator(),
              ],
            ),
            trailing: _buildTrailingButton(),
            isThreeLine: true,
          ),
          
          // Action buttons
          if (_shouldShowActionButtons()) ...[
            const Divider(height: 1),
            _buildActionButtons(context),
          ],
        ],
      ),
    );
  }

  Widget _buildLeadingIcon() {
    IconData iconData;
    Color iconColor;
    
    switch (download.status) {
      case DownloadStatus.downloading:
        iconData = Icons.download;
        iconColor = ThemeProvider.primaryBlue;
        break;
      case DownloadStatus.completed:
        iconData = Icons.check_circle;
        iconColor = ThemeProvider.accentGreen;
        break;
      case DownloadStatus.failed:
        iconData = Icons.error;
        iconColor = ThemeProvider.errorRed;
        break;
      case DownloadStatus.paused:
        iconData = Icons.pause_circle;
        iconColor = ThemeProvider.warningOrange;
        break;
      case DownloadStatus.cancelled:
        iconData = Icons.cancel;
        iconColor = Colors.grey;
        break;
      default:
        iconData = Icons.pending;
        iconColor = Colors.grey;
    }

    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 24,
      ),
    );
  }

  Widget _buildSubtitleRow() {
    return Row(
      children: [
        // Platform badge
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: ThemeProvider.primaryBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            download.platform,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: ThemeProvider.primaryBlue,
            ),
          ),
        ),
        
        const SizedBox(width: 8),
        
        // Quality badge
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            '${download.quality} ${download.format.toUpperCase()}',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
        ),
        
        const Spacer(),
        
        // File size
        Text(
          download.formattedFileSize,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    if (download.status == DownloadStatus.downloading) {
      return Column(
        children: [
          LinearProgressIndicator(
            value: download.progress,
            backgroundColor: Colors.grey.withOpacity(0.2),
            valueColor: const AlwaysStoppedAnimation<Color>(ThemeProvider.primaryBlue),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Downloading...',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                download.formattedProgress,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
        ],
      );
    } else {
      return Row(
        children: [
          _buildStatusChip(),
          const Spacer(),
          Text(
            _formatDate(download.createdAt),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      );
    }
  }

  Widget _buildStatusChip() {
    Color backgroundColor;
    Color textColor;
    String statusText;
    
    switch (download.status) {
      case DownloadStatus.completed:
        backgroundColor = ThemeProvider.accentGreen.withOpacity(0.1);
        textColor = ThemeProvider.accentGreen;
        statusText = 'Completed';
        break;
      case DownloadStatus.failed:
        backgroundColor = ThemeProvider.errorRed.withOpacity(0.1);
        textColor = ThemeProvider.errorRed;
        statusText = 'Failed';
        break;
      case DownloadStatus.paused:
        backgroundColor = ThemeProvider.warningOrange.withOpacity(0.1);
        textColor = ThemeProvider.warningOrange;
        statusText = 'Paused';
        break;
      case DownloadStatus.cancelled:
        backgroundColor = Colors.grey.withOpacity(0.1);
        textColor = Colors.grey;
        statusText = 'Cancelled';
        break;
      default:
        backgroundColor = Colors.grey.withOpacity(0.1);
        textColor = Colors.grey;
        statusText = 'Pending';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildTrailingButton() {
    switch (download.status) {
      case DownloadStatus.downloading:
        return IconButton(
          icon: const Icon(Icons.pause),
          onPressed: () => onAction('pause'),
          tooltip: 'Pause',
        );
      case DownloadStatus.paused:
        return IconButton(
          icon: const Icon(Icons.play_arrow),
          onPressed: () => onAction('resume'),
          tooltip: 'Resume',
        );
      case DownloadStatus.failed:
        return IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => onAction('retry'),
          tooltip: 'Retry',
        );
      case DownloadStatus.completed:
        return IconButton(
          icon: const Icon(Icons.play_circle_filled),
          onPressed: () => onAction('play'),
          tooltip: 'Play',
        );
      default:
        return IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () => _showOptionsMenu(),
        );
    }
  }

  bool _shouldShowActionButtons() {
    return download.status != DownloadStatus.downloading;
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          if (download.status == DownloadStatus.completed) ...[
            _buildActionButton(
              icon: Icons.play_arrow,
              label: 'Play',
              onPressed: () => onAction('play'),
            ),
            _buildActionButton(
              icon: Icons.share,
              label: 'Share',
              onPressed: () => onAction('share'),
            ),
          ],
          
          if (download.status == DownloadStatus.failed) ...[
            _buildActionButton(
              icon: Icons.refresh,
              label: 'Retry',
              onPressed: () => onAction('retry'),
            ),
          ],
          
          if (download.status == DownloadStatus.paused) ...[
            _buildActionButton(
              icon: Icons.play_arrow,
              label: 'Resume',
              onPressed: () => onAction('resume'),
            ),
            _buildActionButton(
              icon: Icons.stop,
              label: 'Cancel',
              onPressed: () => onAction('cancel'),
            ),
          ],
          
          _buildActionButton(
            icon: Icons.info_outline,
            label: 'Info',
            onPressed: () => onAction('info'),
          ),
          
          _buildActionButton(
            icon: Icons.delete_outline,
            label: 'Delete',
            onPressed: () => onAction('delete'),
            isDestructive: true,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 20,
              color: isDestructive ? ThemeProvider.errorRed : null,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isDestructive ? ThemeProvider.errorRed : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showOptionsMenu() {
    // This would show a popup menu with more options
    // Implementation depends on the context
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
