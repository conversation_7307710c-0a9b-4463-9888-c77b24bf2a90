import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:workmanager/workmanager.dart';

import 'core/di/injection_container.dart';
import 'core/config/app_config.dart';
import 'core/services/background_service.dart';
import 'presentation/app/app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize error tracking
  await SentryFlutter.init(
    (options) {
      options.dsn = AppConfig.sentryDsn;
      options.environment = AppConfig.environment;
    },
    appRunner: () => _initializeApp(),
  );
}

Future<void> _initializeApp() async {
  try {
    // Set system UI preferences
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Initialize Hive
    await Hive.initFlutter();

    // Initialize dependency injection
    await configureDependencies();

    // Initialize background work manager
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: AppConfig.isDebug,
    );

    // Initialize background services
    await getIt<BackgroundService>().initialize();

    // Set up global error handling
    FlutterError.onError = (FlutterErrorDetails details) {
      Sentry.captureException(
        details.exception,
        stackTrace: details.stack,
      );
    };

    runApp(const VideoDownloaderApp());
  } catch (error, stackTrace) {
    await Sentry.captureException(error, stackTrace: stackTrace);
    rethrow;
  }
}

/// Background task callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      await configureDependencies();
      final backgroundService = getIt<BackgroundService>();
      return await backgroundService.handleBackgroundTask(task, inputData);
    } catch (error) {
      await Sentry.captureException(error);
      return false;
    }
  });
}
