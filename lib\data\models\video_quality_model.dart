import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

import '../../domain/entities/video_quality.dart';

part 'video_quality_model.freezed.dart';
part 'video_quality_model.g.dart';

@freezed
@HiveType(typeId: 1)
class VideoQualityModel with _$VideoQualityModel {
  const factory VideoQualityModel({
    @HiveField(0) required String quality,
    @HiveField(1) required String format,
    @HiveField(2) required String downloadUrl,
    @HiveField(3) int? fileSize,
    @HiveField(4) required bool hasAudio,
    @HiveField(5) required bool hasVideo,
    @HiveField(6) String? codec,
    @HiveField(7) int? bitrate,
    @HiveField(8) String? resolution,
    @HiveField(9) double? fps,
    @HiveField(10) bool? isHdr,
  }) = _VideoQualityModel;

  factory VideoQualityModel.fromJson(Map<String, dynamic> json) =>
      _$VideoQualityModelFromJson(json);
}

/// Extension to convert model to entity
extension VideoQualityModelX on VideoQualityModel {
  VideoQuality toEntity() {
    return VideoQuality(
      quality: quality,
      format: format,
      downloadUrl: downloadUrl,
      fileSize: fileSize,
      hasAudio: hasAudio,
      hasVideo: hasVideo,
      codec: codec,
      bitrate: bitrate,
      resolution: resolution,
      fps: fps,
      isHdr: isHdr ?? false,
    );
  }
}

/// Extension to convert entity to model
extension VideoQualityX on VideoQuality {
  VideoQualityModel toModel() {
    return VideoQualityModel(
      quality: quality,
      format: format,
      downloadUrl: downloadUrl,
      fileSize: fileSize,
      hasAudio: hasAudio,
      hasVideo: hasVideo,
      codec: codec,
      bitrate: bitrate,
      resolution: resolution,
      fps: fps,
      isHdr: isHdr,
    );
  }
}
