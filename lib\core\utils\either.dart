import 'package:equatable/equatable.dart';

/// Represents a value that can be either a Left (failure) or Right (success)
abstract class Either<L, R> extends Equatable {
  const Either();

  /// Returns true if this is a Left value
  bool get isLeft => this is Left<L, R>;

  /// Returns true if this is a Right value
  bool get isRight => this is Right<L, R>;

  /// Fold the Either into a single value
  T fold<T>(T Function(L left) leftFunction, T Function(R right) rightFunction);

  /// Map the right value
  Either<L, T> map<T>(T Function(R right) mapper);

  /// FlatMap the right value
  Either<L, T> flatMap<T>(Either<L, T> Function(R right) mapper);

  /// Get the right value or throw
  R getOrThrow();

  /// Get the right value or return default
  R getOrElse(R defaultValue);

  /// Get the left value or null
  L? getLeftOrNull();

  /// Get the right value or null
  R? getRightOrNull();
}

/// Left side of Either (represents failure)
class Left<L, R> extends Either<L, R> {
  const Left(this.value);

  final L value;

  @override
  T fold<T>(T Function(L left) leftFunction, T Function(R right) rightFunction) {
    return leftFunction(value);
  }

  @override
  Either<L, T> map<T>(T Function(R right) mapper) {
    return Left<L, T>(value);
  }

  @override
  Either<L, T> flatMap<T>(Either<L, T> Function(R right) mapper) {
    return Left<L, T>(value);
  }

  @override
  R getOrThrow() {
    throw Exception('Called getOrThrow on Left: $value');
  }

  @override
  R getOrElse(R defaultValue) {
    return defaultValue;
  }

  @override
  L? getLeftOrNull() {
    return value;
  }

  @override
  R? getRightOrNull() {
    return null;
  }

  @override
  List<Object?> get props => [value];

  @override
  String toString() => 'Left($value)';
}

/// Right side of Either (represents success)
class Right<L, R> extends Either<L, R> {
  const Right(this.value);

  final R value;

  @override
  T fold<T>(T Function(L left) leftFunction, T Function(R right) rightFunction) {
    return rightFunction(value);
  }

  @override
  Either<L, T> map<T>(T Function(R right) mapper) {
    return Right<L, T>(mapper(value));
  }

  @override
  Either<L, T> flatMap<T>(Either<L, T> Function(R right) mapper) {
    return mapper(value);
  }

  @override
  R getOrThrow() {
    return value;
  }

  @override
  R getOrElse(R defaultValue) {
    return value;
  }

  @override
  L? getLeftOrNull() {
    return null;
  }

  @override
  R? getRightOrNull() {
    return value;
  }

  @override
  List<Object?> get props => [value];

  @override
  String toString() => 'Right($value)';
}

/// Extension methods for Either
extension EitherExtensions<L, R> on Either<L, R> {
  /// Execute a function if this is a Right value
  Either<L, R> onRight(void Function(R value) action) {
    if (isRight) {
      action((this as Right<L, R>).value);
    }
    return this;
  }

  /// Execute a function if this is a Left value
  Either<L, R> onLeft(void Function(L value) action) {
    if (isLeft) {
      action((this as Left<L, R>).value);
    }
    return this;
  }
}
