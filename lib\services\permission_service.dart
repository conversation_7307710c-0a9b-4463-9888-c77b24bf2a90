import 'dart:io';
import 'package:permission_handler/permission_handler.dart';

class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  // Check if all required permissions are granted
  Future<bool> hasAllPermissions() async {
    final permissions = await _getRequiredPermissions();

    for (final permission in permissions) {
      final status = await permission.status;
      if (!status.isGranted) {
        return false;
      }
    }

    return true;
  }

  // Request all required permissions
  Future<Map<Permission, PermissionStatus>> requestAllPermissions() async {
    final permissions = await _getRequiredPermissions();
    return await permissions.request();
  }

  // Request specific permission with explanation
  Future<PermissionStatus> requestPermissionWithRationale({
    required Permission permission,
    required String title,
    required String message,
    required Function() onGranted,
    required Function() onDenied,
    Function()? onPermanentlyDenied,
  }) async {
    final status = await permission.status;

    if (status.isGranted) {
      onGranted();
      return status;
    }

    if (status.isDenied) {
      // Show rationale dialog here if needed
      final result = await permission.request();

      if (result.isGranted) {
        onGranted();
      } else if (result.isPermanentlyDenied) {
        onPermanentlyDenied?.call() ?? _showSettingsDialog();
      } else {
        onDenied();
      }

      return result;
    }

    if (status.isPermanentlyDenied) {
      onPermanentlyDenied?.call() ?? _showSettingsDialog();
      return status;
    }

    return status;
  }

  // Get required permissions based on platform and Android version
  Future<List<Permission>> _getRequiredPermissions() async {
    final List<Permission> permissions = [];

    if (Platform.isAndroid) {
      // Always required - Note: Internet permissions are automatically granted
      // Permission.internet and Permission.accessNetworkState don't exist in permission_handler
      // These are automatically granted permissions in Android

      // Storage permissions based on Android version
      final androidInfo = await _getAndroidVersion();

      if (androidInfo >= 33) {
        // Android 13+ (API 33+)
        permissions.addAll([
          Permission.videos,
          Permission.audio,
          Permission.notification,
          Permission.manageExternalStorage,
        ]);
      } else if (androidInfo >= 30) {
        // Android 11+ (API 30+)
        permissions.addAll([
          Permission.manageExternalStorage,
          Permission.notification,
        ]);
      } else {
        // Android 10 and below
        permissions.addAll([
          Permission.storage,
          Permission.accessMediaLocation,
        ]);
      }

      // Additional permissions
      permissions.addAll([Permission.notification]);
    }

    if (Platform.isIOS) {
      permissions.addAll([Permission.photos, Permission.notification]);
    }

    return permissions;
  }

  Future<int> _getAndroidVersion() async {
    // This would typically get the actual Android API level
    // For now, return a default value
    return 33; // Assume Android 13+
  }

  // Check specific permissions
  Future<bool> hasStoragePermission() async {
    if (Platform.isAndroid) {
      final androidVersion = await _getAndroidVersion();

      if (androidVersion >= 33) {
        return await Permission.videos.isGranted &&
            await Permission.audio.isGranted &&
            await Permission.manageExternalStorage.isGranted;
      } else if (androidVersion >= 30) {
        return await Permission.manageExternalStorage.isGranted;
      } else {
        return await Permission.storage.isGranted &&
            await Permission.accessMediaLocation.isGranted;
      }
    }

    if (Platform.isIOS) {
      return await Permission.photos.isGranted;
    }

    return false;
  }

  Future<bool> hasNotificationPermission() async {
    return await Permission.notification.isGranted;
  }

  Future<bool> hasNetworkPermission() async {
    // Internet permission is automatically granted on Android
    return true;
  }

  // Request specific permissions
  Future<PermissionStatus> requestStoragePermission() async {
    if (Platform.isAndroid) {
      final androidVersion = await _getAndroidVersion();

      if (androidVersion >= 33) {
        final videoStatus = await Permission.videos.request();
        final audioStatus = await Permission.audio.request();
        final manageStatus = await Permission.manageExternalStorage.request();
        return videoStatus.isGranted &&
                audioStatus.isGranted &&
                manageStatus.isGranted
            ? PermissionStatus.granted
            : PermissionStatus.denied;
      } else if (androidVersion >= 30) {
        return await Permission.manageExternalStorage.request();
      } else {
        final storageStatus = await Permission.storage.request();
        final mediaStatus = await Permission.accessMediaLocation.request();
        return storageStatus.isGranted && mediaStatus.isGranted
            ? PermissionStatus.granted
            : PermissionStatus.denied;
      }
    }

    if (Platform.isIOS) {
      return await Permission.photos.request();
    }

    return PermissionStatus.granted;
  }

  Future<PermissionStatus> requestNotificationPermission() async {
    return await Permission.notification.request();
  }

  // Permission status helpers
  String getPermissionStatusMessage(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 'Permission granted';
      case PermissionStatus.denied:
        return 'Permission denied';
      case PermissionStatus.restricted:
        return 'Permission restricted';
      case PermissionStatus.limited:
        return 'Permission limited';
      case PermissionStatus.permanentlyDenied:
        return 'Permission permanently denied';
      case PermissionStatus.provisional:
        return 'Permission provisional';
    }
  }

  String getPermissionDescription(Permission permission) {
    switch (permission) {
      case Permission.storage:
      case Permission.manageExternalStorage:
      case Permission.videos:
      case Permission.audio:
        return 'Required to save downloaded videos to your device';
      case Permission.notification:
        return 'Shows download progress and completion notifications';
      default:
        return 'Required for app functionality';
    }
  }

  String getPermissionTitle(Permission permission) {
    switch (permission) {
      case Permission.storage:
      case Permission.manageExternalStorage:
      case Permission.videos:
      case Permission.audio:
        return 'Storage Access';
      case Permission.notification:
        return 'Notifications';
      default:
        return 'Permission Required';
    }
  }

  // Show settings dialog for permanently denied permissions
  Future<void> _showSettingsDialog() async {
    // This would show a dialog to open app settings
    // Implementation depends on your UI framework
    await openAppSettings();
  }

  // Check if permission is critical (app won't work without it)
  bool isCriticalPermission(Permission permission) {
    return [
      Permission.storage,
      Permission.manageExternalStorage,
      Permission.videos,
      Permission.audio,
      Permission.accessMediaLocation,
    ].contains(permission);
  }

  // Get permission explanations for onboarding
  Map<String, String> getPermissionExplanations() {
    return {
      'Storage':
          'We need access to your device storage to save downloaded videos. Your videos will be saved in a dedicated folder that you can access anytime.',
      'Notifications':
          'We\'ll send you notifications to keep you updated on download progress and when your videos are ready to watch.',
      'Network':
          'Internet access is required to download videos from various platforms. We only use this to fetch the content you request.',
      'Media':
          'Access to media files allows us to organize your downloads and integrate with your device\'s media library.',
    };
  }

  // Batch permission request with detailed feedback
  Future<Map<String, bool>> requestPermissionsWithFeedback() async {
    final permissions = await _getRequiredPermissions();
    final results = <String, bool>{};

    for (final permission in permissions) {
      final status = await permission.request();
      final title = getPermissionTitle(permission);
      results[title] = status.isGranted;
    }

    return results;
  }

  // Check if app can function with current permissions
  Future<bool> canAppFunction() async {
    final storagePermission = await hasStoragePermission();
    final networkPermission = await hasNetworkPermission();

    return storagePermission && networkPermission;
  }

  // Get missing critical permissions
  Future<List<Permission>> getMissingCriticalPermissions() async {
    final permissions = await _getRequiredPermissions();
    final missing = <Permission>[];

    for (final permission in permissions) {
      if (isCriticalPermission(permission)) {
        final status = await permission.status;
        if (!status.isGranted) {
          missing.add(permission);
        }
      }
    }

    return missing;
  }
}
