import 'package:flutter/material.dart';
import '../models/video_info.dart';
import '../models/download_item.dart';
import '../services/video_extractor.dart';
import 'download_fix_helper.dart';

/// Manager class to handle app-wide fixes and improvements
class AppFixManager {
  /// Shows a comprehensive error dialog with retry options
  static Future<bool?> showErrorDialog(
    BuildContext context, {
    required String title,
    required String message,
    bool showRetry = true,
    String? details,
  }) async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red[700]),
            const SizedBox(width: 8),
            Expanded(child: Text(title)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            if (details != null) ...[
              const SizedBox(height: 16),
              ExpansionTile(
                title: const Text('تفاصيل الخطأ'),
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      details,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          if (showRetry)
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('إعادة المحاولة'),
            ),
        ],
      ),
    );
  }

  /// Shows a success message with action options
  static void showSuccessSnackBar(
    BuildContext context, {
    required String message,
    String? actionLabel,
    VoidCallback? onAction,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        action: actionLabel != null && onAction != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: Colors.white,
                onPressed: onAction,
              )
            : null,
      ),
    );
  }

  /// Shows a warning message
  static void showWarningSnackBar(
    BuildContext context, {
    required String message,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.warning, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// Shows an error message
  static void showErrorSnackBar(
    BuildContext context, {
    required String message,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// Attempts to fix a failed video extraction
  static Future<VideoInfo?> fixVideoExtraction(String url) async {
    try {
      // Try normal extraction first
      final videoInfo = await VideoExtractor.extractVideoInfo(url);
      if (videoInfo != null && DownloadFixHelper.isValidForDownload(videoInfo)) {
        return videoInfo;
      }

      // If extraction failed, generate fallback
      final fallbackInfo = DownloadFixHelper.generateFallbackVideoInfo(url);
      return fallbackInfo;
    } catch (e) {
      // Last resort - generate basic fallback
      return DownloadFixHelper.generateFallbackVideoInfo(url);
    }
  }

  /// Gets user-friendly error message for download failures
  static String getDownloadErrorMessage(DownloadItem item) {
    if (item.errorMessage == null) return 'خطأ غير معروف';

    final error = item.errorMessage!.toLowerCase();
    
    if (error.contains('connection') || error.contains('network')) {
      return 'مشكلة في الاتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.';
    }
    
    if (error.contains('timeout')) {
      return 'انتهت مهلة الاتصال. حاول مرة أخرى لاحقاً.';
    }
    
    if (error.contains('server') || error.contains('404') || error.contains('403')) {
      return 'الخادم غير متاح حالياً. حاول مرة أخرى لاحقاً.';
    }
    
    if (error.contains('url') || error.contains('invalid')) {
      return 'رابط التحميل غير صالح. حاول استخراج الفيديو مرة أخرى.';
    }
    
    if (error.contains('space') || error.contains('storage')) {
      return 'مساحة التخزين غير كافية. احذف بعض الملفات وحاول مرة أخرى.';
    }
    
    if (error.contains('permission')) {
      return 'لا توجد صلاحيات كافية للكتابة. تحقق من إعدادات التطبيق.';
    }
    
    return 'فشل التحميل: ${item.errorMessage}';
  }

  /// Gets suggestions for fixing download issues
  static List<String> getDownloadFixSuggestions(DownloadItem item) {
    final suggestions = <String>[];
    
    if (item.errorMessage == null) {
      suggestions.add('حاول إعادة المحاولة');
      return suggestions;
    }

    final error = item.errorMessage!.toLowerCase();
    
    if (error.contains('connection') || error.contains('network')) {
      suggestions.addAll([
        'تحقق من اتصال الإنترنت',
        'جرب شبكة Wi-Fi مختلفة',
        'أعد تشغيل التطبيق',
      ]);
    } else if (error.contains('url') || error.contains('invalid')) {
      suggestions.addAll([
        'انسخ الرابط مرة أخرى',
        'تأكد من أن الفيديو ما زال متاحاً',
        'جرب رابط فيديو آخر',
      ]);
    } else if (error.contains('space') || error.contains('storage')) {
      suggestions.addAll([
        'احذف بعض الملفات لتوفير مساحة',
        'انقل الملفات إلى بطاقة SD',
        'امسح ذاكرة التخزين المؤقت',
      ]);
    } else {
      suggestions.addAll([
        'حاول إعادة المحاولة',
        'أعد تشغيل التطبيق',
        'تحقق من إعدادات التطبيق',
      ]);
    }
    
    return suggestions;
  }

  /// Shows a comprehensive fix dialog for failed downloads
  static Future<String?> showDownloadFixDialog(
    BuildContext context,
    DownloadItem item,
  ) async {
    final errorMessage = getDownloadErrorMessage(item);
    final suggestions = getDownloadFixSuggestions(item);

    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إصلاح مشكلة التحميل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المشكلة:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(errorMessage),
            const SizedBox(height: 16),
            Text(
              'الحلول المقترحة:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            ...suggestions.map((suggestion) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('• '),
                  Expanded(child: Text(suggestion)),
                ],
              ),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop('retry'),
            child: const Text('إعادة المحاولة'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop('extract_again'),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
            child: const Text('استخراج مرة أخرى'),
          ),
        ],
      ),
    );
  }
}
