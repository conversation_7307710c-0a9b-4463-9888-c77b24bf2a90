import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import '../../core/di/injection_container.dart';
import '../../core/config/app_config.dart';
import '../blocs/video_extraction/video_extraction_bloc.dart';
import '../blocs/download/download_bloc.dart';
import '../blocs/theme/theme_bloc.dart';
import '../blocs/app/app_bloc.dart';
import '../theme/app_theme.dart';
import '../routes/app_router.dart';

/// Main application widget
class VideoDownloaderApp extends StatelessWidget {
  const VideoDownloaderApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AppBloc>(
          create: (_) => getIt<AppBloc>()..add(const AppStarted()),
        ),
        BlocProvider<ThemeBloc>(
          create: (_) => getIt<ThemeBloc>()..add(const ThemeInitialized()),
        ),
        BlocProvider<VideoExtractionBloc>(
          create: (_) => getIt<VideoExtractionBloc>(),
        ),
        BlocProvider<DownloadBloc>(
          create: (_) => getIt<DownloadBloc>()..add(const DownloadInitialized()),
        ),
      ],
      child: BlocBuilder<ThemeBloc, ThemeState>(
        builder: (context, themeState) {
          return MaterialApp.router(
            title: AppConfig.appName,
            debugShowCheckedModeBanner: false,

            // Localization
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', 'US'), // English
              Locale('ar', 'SA'), // Arabic
            ],

            // Theme
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeState.themeMode,

            // Routing
            routerConfig: AppRouter.router,

            // System UI
            builder: (context, child) {
              return AnnotatedRegion<SystemUiOverlayStyle>(
                value: themeState.themeMode == ThemeMode.dark
                    ? SystemUiOverlayStyle.light
                    : SystemUiOverlayStyle.dark,
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}
