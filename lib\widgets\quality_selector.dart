import 'package:flutter/material.dart';
import '../models/video_info.dart';
import '../providers/theme_provider.dart';

class QualitySelector extends StatelessWidget {
  final List<VideoQuality> qualities;
  final VideoQuality? selectedQuality;
  final Function(VideoQuality) onQualitySelected;

  const QualitySelector({
    super.key,
    required this.qualities,
    required this.selectedQuality,
    required this.onQualitySelected,
  });

  @override
  Widget build(BuildContext context) {
    if (qualities.isEmpty) {
      return const Center(
        child: Text(
          'No qualities available',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    // Group qualities by type
    final videoQualities = qualities.where((q) => q.hasVideo && q.hasAudio).toList();
    final videoOnlyQualities = qualities.where((q) => q.hasVideo && !q.hasAudio).toList();
    final audioOnlyQualities = qualities.where((q) => !q.hasVideo && q.hasAudio).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Video + Audio qualities
        if (videoQualities.isNotEmpty) ...[
          _buildQualityGroup(
            context,
            'Video + Audio',
            videoQualities,
            Icons.videocam,
            ThemeProvider.primaryBlue,
          ),
        ],
        
        // Video only qualities
        if (videoOnlyQualities.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildQualityGroup(
            context,
            'Video Only',
            videoOnlyQualities,
            Icons.video_file,
            ThemeProvider.warningOrange,
          ),
        ],
        
        // Audio only qualities
        if (audioOnlyQualities.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildQualityGroup(
            context,
            'Audio Only',
            audioOnlyQualities,
            Icons.audiotrack,
            ThemeProvider.accentGreen,
          ),
        ],
      ],
    );
  }

  Widget _buildQualityGroup(
    BuildContext context,
    String title,
    List<VideoQuality> groupQualities,
    IconData icon,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Group header
        Row(
          children: [
            Icon(icon, size: 18, color: color),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Quality options
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: groupQualities.map((quality) {
            final isSelected = selectedQuality == quality;
            
            return GestureDetector(
              onTap: () => onQualitySelected(quality),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? color : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? color : Colors.grey.withOpacity(0.3),
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Quality indicator
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.white : color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    
                    // Quality text
                    Text(
                      quality.quality,
                      style: TextStyle(
                        color: isSelected ? Colors.white : null,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        fontSize: 14,
                      ),
                    ),
                    
                    // Format badge
                    if (quality.format.isNotEmpty) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: isSelected 
                              ? Colors.white.withOpacity(0.2)
                              : color.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          quality.format.toUpperCase(),
                          style: TextStyle(
                            color: isSelected ? Colors.white : color,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                    
                    // File size
                    if (quality.fileSize != null) ...[
                      const SizedBox(width: 8),
                      Text(
                        _formatFileSize(quality.fileSize!),
                        style: TextStyle(
                          color: isSelected 
                              ? Colors.white.withOpacity(0.8)
                              : Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}
